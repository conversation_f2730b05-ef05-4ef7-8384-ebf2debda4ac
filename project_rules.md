# World-Cloud-Plus 项目开发规范

## 📋 项目概述

World-Cloud-Plus 是一个基于 Spring Boot 3.x + Vue3 的微服务架构企业级管理系统，采用前后端分离架构，支持多租户、分布式部署。

### 技术栈
- **后端**: Spring Boot 3.4.6, Spring Cloud 2024.0.0, JDK 17+
- **前端**: Vue3 + TypeScript + Element Plus
- **数据库**: MySQL 5.7+, Oracle 12c+, PostgreSQL 13+
- **缓存**: Redis 6.x+
- **消息队列**: RocketMQ 2.3.0
- **工作流**: Warm-Flow 1.7.2
- **文档**: SpringDoc + Javadoc
- **构建工具**: Maven 3.8+

---

## 🏗️ 项目架构规范

### 1. 模块结构规范

```
world-cloud-plus/
├── world-auth/                # 认证服务模块
├── world-gateway/            # 网关服务
├── world-api/                # API接口模块
│   ├── world-api-system/     # 系统API
│   ├── world-api-resource/   # 资源API
│   ├── world-api-sfc/        # SFC API
│   └── world-api-workflow/   # 工作流API
├── world-common/             # 公共模块
│   ├── world-common-core/    # 核心工具
│   ├── world-common-web/     # Web工具
│   ├── world-common-mybatis/ # MyBatis工具
│   ├── world-common-redis/   # Redis工具
│   ├── world-common-excel/   # Excel工具
│   └── ...                   # 其他公共模块
├── world-modules/            # 业务模块
│   ├── world-system/         # 系统管理
│   ├── world-gen/           # 代码生成
│   ├── world-resource/      # 资源服务
│   ├── world-job/           # 定时任务
│   └── world-workflow/      # 工作流
├── world-visual/            # 可视化模块
│   ├── world-monitor/       # 系统监控
│   ├── world-nacos/         # 配置中心
│   ├── world-sentinel-dashboard/ # 限流控制台
│   └── world-seata-server/  # 分布式事务
├── world-example/           # 示例模块
│   ├── world-sfc/          # 装配制造示例
│   ├── world-imes/         # 生产制造示例
│   ├── world-pmc/          # PMC管理示例
│   └── world-demo/         # 其他示例
└── script/                 # 脚本文件
    ├── config/            # 配置文件
    ├── docker/            # Docker配置
    └── sql/              # 数据库脚本
```

### 2. 包命名规范

- **根包名**: `org.dromara`
- **模块包名**: `org.dromara.{模块名}`
- **功能包名**: `org.dromara.{模块名}.{功能名}`

示例：
```java
org.dromara.mes.controller.PdfBarcodeController
org.dromara.mes.service.IPdfBarcodeService
org.dromara.mes.service.impl.PdfBarcodeServiceImpl
org.dromara.mes.domain.WorkCenter
org.dromara.mes.mapper.WorkCenterMapper
```

---

## 💻 代码开发规范

### 1. Java 代码规范

#### 1.1 类命名规范
- **Controller**: `{业务名}Controller` (如: `PdfBarcodeController`)
- **Service接口**: `I{业务名}Service` (如: `IPdfBarcodeService`)
- **Service实现**: `{业务名}ServiceImpl` (如: `PdfBarcodeServiceImpl`)
- **Entity**: `{业务名}` (如: `WorkCenter`)
- **VO**: `{业务名}Vo` (如: `PdfBarcodeStatusVo`)
- **DTO**: `{业务名}DTO` (如: `ProcessStatisticsQueryDTO`)
- **Mapper**: `{业务名}Mapper` (如: `WorkCenterMapper`)
- **Utils**: `{功能名}Utils` (如: `PdfBarcodeProcessor`)

#### 1.2 注解使用规范

**Controller层注解**:
```java
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdfBarcode")
public class PdfBarcodeController extends BaseController {
    
    @SaCheckPermission("mes:pdfBarcode:upload")
    @Log(title = "PDF条形码处理", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public void uploadAndProcess(...) {
        // 业务逻辑
    }
}
```

**Service层注解**:
```java
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfBarcodeServiceImpl implements IPdfBarcodeService {
    // 业务实现
}
```

**Entity注解**:
```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("work_center")
public class WorkCenter extends TenantEntity {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id")
    private Long id;
    
    @TableLogic
    private String delFlag;
}
```

#### 1.3 继承规范

- **Controller**: 必须继承 `BaseController`
- **Entity**: 根据需求继承 `BaseEntity` 或 `TenantEntity`
- **Mapper**: 继承 `BaseMapperPlus<T>`

#### 1.4 响应格式规范

统一使用 `R<T>` 作为响应格式：
```java
// 成功响应
return R.ok(data);
return R.ok("操作成功");
return R.ok("操作成功", data);

// 失败响应
return R.fail("操作失败");
return R.fail("操作失败", data);
return R.fail(code, "错误信息");

// 警告响应
return R.warn("警告信息");
return R.warn("警告信息", data);
```

### 2. 前端代码规范

#### 2.1 Vue3 + TypeScript 规范

**组件结构**:
```vue
<template>
  <div class="p-6">
    <el-card class="max-w-3xl mx-auto" shadow="hover">
      <!-- 页面内容 -->
    </el-card>
  </div>
</template>

<script setup name="ComponentName" lang="ts">
import { ComponentInternalInstance, getCurrentInstance, reactive, ref, toRefs } from 'vue';
import { ElMessage } from 'element-plus';

// 类型定义
type FormType = {
  field1: string;
  field2: number;
};

// 响应式数据
const formData = reactive<FormType>({
  field1: '',
  field2: 0
});

// 方法定义
const handleSubmit = async () => {
  // 业务逻辑
};
</script>

<style scoped>
/* 样式定义 */
</style>
```

#### 2.2 API 调用规范

```typescript
// 使用 fetch API 进行文件上传
const formData = new FormData();
files.forEach(file => {
  if (file.raw) formData.append('files', file.raw);
});

const response = await fetch(`${baseURL}/api/endpoint`, {
  method: 'POST',
  headers: { ...globalHeaders() },
  body: formData
});
```

---

## 🗄️ 数据库规范

### 1. 表命名规范

- **表名**: 小写字母 + 下划线 (如: `work_center`, `pdf_convert_record`)
- **字段名**: 小写字母 + 下划线 (如: `create_time`, `update_by`)
- **主键**: 统一使用 `id` (Long类型，雪花ID)
- **逻辑删除**: 统一使用 `del_flag` (String类型，'0'存在，'1'删除)

### 2. 字段规范

**基础字段** (继承自BaseEntity):
```sql
id BIGINT PRIMARY KEY COMMENT '主键ID',
create_dept BIGINT COMMENT '创建部门',
create_by BIGINT COMMENT '创建者',
create_time DATETIME COMMENT '创建时间',
update_by BIGINT COMMENT '更新者',
update_time DATETIME COMMENT '更新时间',
del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）'
```

**多租户字段** (继承自TenantEntity):
```sql
tenant_id VARCHAR(20) COMMENT '租户编号'
```

### 3. 索引规范

- **主键索引**: 自动创建
- **唯一索引**: `uk_{表名}_{字段名}`
- **普通索引**: `idx_{表名}_{字段名}`
- **复合索引**: `idx_{表名}_{字段1}_{字段2}`

---

## 🔧 配置规范

### 1. 配置文件结构

```
script/config/nacos/
├── application-common.yml    # 公共配置
├── world-auth.yml           # 认证服务配置
├── world-gateway.yml        # 网关配置
├── world-system.yml         # 系统服务配置
└── ...                     # 其他服务配置
```

### 2. 配置项规范

**服务基础配置**:
```yaml
server:
  port: 9200
  undertow:
    max-http-post-size: -1
    buffer-size: 512
    direct-buffers: true
    threads:
      io: 8
      worker: 256

spring:
  application:
    name: world-imes
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      discovery:
        server-addr: @nacos.server@
        group: @nacos.discovery.group@
      config:
        server-addr: @nacos.server@
        group: @nacos.config.group@
        username: @nacos.username@
        password: @nacos.password@
```

### 3. 环境配置

- **开发环境**: `dev` (默认)
- **生产环境**: `prod`
- **测试环境**: `test`

---

## 🔐 安全规范

### 1. 权限控制

**Sa-Token权限注解**:
```java
@SaCheckPermission("mes:pdfBarcode:upload")    // 权限校验
@SaCheckRole("admin")                          // 角色校验
@SaCheckLogin                                  // 登录校验
@SaCheckSafe                                   // 二级认证校验
```

**权限命名规范**:
- 格式: `{模块}:{功能}:{操作}`
- 示例: `mes:pdfBarcode:upload`, `system:user:add`

### 2. 数据脱敏

```java
@Sensitive(type = SensitiveType.MOBILE_PHONE)
private String phone;

@Sensitive(type = SensitiveType.ID_CARD)
private String idCard;
```

### 3. 数据加密

```java
@EncryptField
private String sensitiveData;
```

---

## 📝 日志规范

### 1. 日志级别

- **ERROR**: 系统错误，需要立即处理
- **WARN**: 警告信息，需要关注
- **INFO**: 重要业务信息
- **DEBUG**: 调试信息

### 2. 日志格式

```java
@Slf4j
public class ExampleService {
    
    public void processData() {
        log.info("开始处理数据，参数: {}", param);
        
        try {
            // 业务逻辑
            log.info("数据处理成功，结果: {}", result);
        } catch (Exception e) {
            log.error("数据处理失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
```

### 3. 日志配置

```yaml
logging:
  level:
    org.springframework: warn
    org.apache.dubbo: warn
    com.alibaba.nacos: warn
    org.mybatis.spring.mapper: error
  config: classpath:logback-plus.xml
```

---

## 🧪 测试规范

### 1. 单元测试

```java
@SpringBootTest
@Tag("dev")  // 环境标签
class PdfBarcodeServiceTest {
    
    @Autowired
    private IPdfBarcodeService pdfBarcodeService;
    
    @Test
    void testProcessPdfFiles() {
        // 测试逻辑
    }
}
```

### 2. 集成测试

- 使用 `@Tag("test")` 标记集成测试
- 测试数据使用 `@Sql` 注解准备
- 测试完成后清理数据

---

## 📦 构建部署规范

### 1. Maven配置

**版本管理**:
```xml
<properties>
    <revision>2.3.0</revision>
    <java.version>17</java.version>
    <spring-boot.version>3.4.6</spring-boot.version>
    <spring-cloud.version>2024.0.0</spring-cloud.version>
</properties>
```

**插件配置**:
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.14.0</version>
    <configuration>
        <source>${java.version}</source>
        <target>${java.version}</target>
        <encoding>UTF-8</encoding>
    </configuration>
</plugin>
```

### 2. Docker部署

**Dockerfile规范**:
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app

COPY target/*.jar app.jar

EXPOSE 9200

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 3. 环境变量

```bash
# 环境标识
PROFILES_ACTIVE=dev

# Nacos配置
NACOS_SERVER=127.0.0.1:8848
NACOS_USERNAME=nacos
NACOS_PASSWORD=nacos

# Redis配置
REDIS_HOST=**************
REDIS_PORT=18884
REDIS_PASSWORD=123456
```

---

## 📚 文档规范

### 1. API文档

**使用SpringDoc + Javadoc**:
```java
/**
 * PDF条形码控制器
 * 前端访问路由地址为:/mes/pdfBarcode
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/pdfBarcode")
public class PdfBarcodeController {
    
    /**
     * 批量上传PDF文件并处理
     * 
     * @param files PDF文件数组
     * @param barcodeHorizontal 条形码水平位置
     * @param barcodeX 边距
     * @param barcodeY 上边距
     * @param barcodeWidth 宽度
     * @param barcodeHeight 高度
     * @param response HTTP响应
     */
    @PostMapping("/upload")
    public void uploadAndProcess(...) {
        // 实现
    }
}
```

### 2. 代码注释

- **类注释**: 说明类的作用、作者、创建时间
- **方法注释**: 说明方法功能、参数、返回值
- **复杂逻辑注释**: 解释业务逻辑和算法

### 3. README文档

每个模块应包含：
- 功能说明
- 快速开始
- API文档链接
- 配置说明
- 常见问题

---

## 🔄 版本控制规范

### 1. Git提交规范

**提交信息格式**:
```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型说明**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:
```
feat(mes): 添加PDF条形码生成功能

- 支持批量PDF文件上传
- 自动生成Code128A格式条形码
- 支持自定义条形码位置和尺寸
- 提供ZIP包下载功能

Closes #123
```

### 2. 分支管理

- **main**: 主分支，生产环境代码
- **develop**: 开发分支，集成测试代码
- **feature/**: 功能分支
- **hotfix/**: 紧急修复分支
- **release/**: 发布分支

### 3. 代码审查

- 所有代码必须经过Code Review
- 使用Pull Request进行代码合并
- 确保测试覆盖率达标

---

## 🚀 性能优化规范

### 1. 数据库优化

- 合理使用索引
- 避免N+1查询问题
- 使用分页查询
- 适当使用缓存

### 2. 接口优化

- 使用异步处理大文件
- 实现接口限流
- 合理使用缓存
- 优化SQL查询

### 3. 前端优化

- 使用懒加载
- 图片压缩
- 代码分割
- 缓存策略

---

## 🛡️ 异常处理规范

### 1. 异常分类

- **业务异常**: 继承 `ServiceException`
- **系统异常**: 继承 `RuntimeException`
- **参数异常**: 使用 `@Validated` 注解

### 2. 异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ServiceException.class)
    public R<Void> handleServiceException(ServiceException e) {
        return R.fail(e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        return R.fail("系统异常，请联系管理员");
    }
}
```

---

## 📋 代码质量规范

### 1. 代码检查

- 使用Alibaba代码规范
- 集成SonarQube代码质量检查
- 使用Checkstyle进行代码格式检查

### 2. 代码覆盖率

- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖核心业务流程
- 使用JaCoCo生成覆盖率报告

### 3. 性能监控

- 使用Spring Boot Actuator监控
- 集成Micrometer指标收集
- 使用SkyWalking链路追踪

---

## 🔧 开发工具规范

### 1. IDE配置

- **推荐IDE**: IntelliJ IDEA 2024.3+
- **代码格式化**: 使用项目统一配置
- **插件推荐**: Lombok, MyBatis, Spring Boot

### 2. 开发环境

- **JDK版本**: 17+
- **Maven版本**: 3.8+
- **Node.js版本**: 18.18+
- **数据库**: MySQL 8.0+

### 3. 调试工具

- 使用Postman进行API测试
- 使用Redis Desktop Manager查看缓存
- 使用Nacos控制台管理配置

---

## 📞 联系方式

- **项目维护**: World团队
- **技术支持**: <EMAIL>
- **文档地址**: [项目文档](https://plus-doc.dromara.org)

---

## 📝 更新日志

### v2.3.0 (2025-01-27)
- ✅ 完善项目开发规范文档
- ✅ 统一代码风格和命名规范
- ✅ 规范API接口设计
- ✅ 完善数据库设计规范
- ✅ 统一配置管理规范

---

*最后更新时间: 2025-01-27*
*文档版本: v1.0.0*


