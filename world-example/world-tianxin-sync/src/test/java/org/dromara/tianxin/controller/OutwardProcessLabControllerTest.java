package org.dromara.tianxin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.dromara.tianxin.domain.dto.MailSendDto;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 外发加工标签控制器测试类
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@WebMvcTest(OutwardProcessLabController.class)
class OutwardProcessLabControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IOutwardProcessLabService outwardProcessLabService;

    @Autowired
    private ObjectMapper objectMapper;

    private OutwardProcessLabVo mockProcessLabVo;

    @BeforeEach
    void setUp() {
        // 创建模拟的外发加工标签数据
        mockProcessLabVo = new OutwardProcessLabVo();
        mockProcessLabVo.setId(1L);
        mockProcessLabVo.setPrNo("PR202509001");
        mockProcessLabVo.setPrdNo("ABC123");
        mockProcessLabVo.setPrdDesc("精密零件加工");
        mockProcessLabVo.setQty(100L);
        mockProcessLabVo.setUt("PCS");
        mockProcessLabVo.setVendorName("精密加工厂");
        mockProcessLabVo.setVendorCode("V001");
        mockProcessLabVo.setSta("进行中");
        mockProcessLabVo.setOutsourcingDate(new Date());
    }

    @Test
    void testSendSimpleTextMail() throws Exception {
        // 准备测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("<EMAIL>");
        mailSendDto.setSubject("测试邮件");
        mailSendDto.setContent("这是一封测试邮件");
        mailSendDto.setIsHtml(false);

        // 执行测试
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("邮件发送成功"));
    }

    @Test
    void testSendHtmlMailWithProcessLabInfo() throws Exception {
        // 模拟服务层返回数据
        when(outwardProcessLabService.queryById(anyLong())).thenReturn(mockProcessLabVo);

        // 准备测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("<EMAIL>");
        mailSendDto.setCc("<EMAIL>");
        mailSendDto.setSubject("外发加工标签信息");
        mailSendDto.setContent("<h2>外发加工通知</h2><p>请查收相关信息：</p>");
        mailSendDto.setIsHtml(true);
        mailSendDto.setProcessLabIds(Arrays.asList(1L, 2L));

        // 执行测试
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("邮件发送成功"));
    }

    @Test
    void testSendMailWithInvalidEmail() throws Exception {
        // 准备无效邮箱的测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("invalid-email");
        mailSendDto.setSubject("测试邮件");
        mailSendDto.setContent("这是一封测试邮件");

        // 执行测试，期望返回400错误
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSendMailWithEmptyTo() throws Exception {
        // 准备空收件人的测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("");
        mailSendDto.setSubject("测试邮件");
        mailSendDto.setContent("这是一封测试邮件");

        // 执行测试，期望返回400错误
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSendMailWithEmptySubject() throws Exception {
        // 准备空主题的测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("<EMAIL>");
        mailSendDto.setSubject("");
        mailSendDto.setContent("这是一封测试邮件");

        // 执行测试，期望返回400错误
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSendMailWithEmptyContent() throws Exception {
        // 准备空内容的测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("<EMAIL>");
        mailSendDto.setSubject("测试邮件");
        mailSendDto.setContent("");

        // 执行测试，期望返回400错误
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testSendMailWithMultipleRecipients() throws Exception {
        // 准备多收件人的测试数据
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo("<EMAIL>,<EMAIL>");
        mailSendDto.setCc("<EMAIL>");
        mailSendDto.setBcc("<EMAIL>");
        mailSendDto.setSubject("多收件人测试邮件");
        mailSendDto.setContent("这是一封发送给多个收件人的测试邮件");
        mailSendDto.setIsHtml(false);

        // 执行测试
        mockMvc.perform(post("/processLab/sendMail")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mailSendDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("邮件发送成功"));
    }
}
