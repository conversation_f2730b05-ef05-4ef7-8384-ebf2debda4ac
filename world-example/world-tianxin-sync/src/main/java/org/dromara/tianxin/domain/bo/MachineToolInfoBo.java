package org.dromara.tianxin.domain.bo;

import org.dromara.tianxin.domain.MachineToolInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等业务对象 machine_tool_info
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MachineToolInfo.class, reverseConvertGenerate = false)
public class MachineToolInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 机床
     */
    private String machineTool;

    /**
     * 资源
     */
    private String resource;

    /**
     * 序号
     */
    private Long serialNo;

    /**
     * 可扫描
     */
    private String isScannable;

    /**
     * 分组
     */
    private String grouping;

    /**
     * 类别
     */
    private String category;

    /**
     * 使用状态
     */
    private String usageStatus;

    /**
     * 停用日期
     */
    private Date deactivationDate;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operationTime;


}
