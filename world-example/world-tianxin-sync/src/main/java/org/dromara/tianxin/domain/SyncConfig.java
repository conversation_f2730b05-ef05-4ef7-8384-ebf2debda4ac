package org.dromara.tianxin.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 天心天思数据同步配置对象 t_sync_config
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sync_config")
public class SyncConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 模块名称
     */
    @TableField("module")
    private String module;

    /**
     * 配置键
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置说明
     */
    @TableField("description")
    private String description;

    /**
     * 是否启用：1-启用，0-禁用
     */
    @TableField("enabled")
    private Integer enabled;
}
