package org.dromara.tianxin.mapper;

import org.dromara.tianxin.domain.ProductScheduleInfo;
import org.dromara.tianxin.domain.vo.ProductScheduleInfoVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface ProductScheduleInfoMapper extends BaseMapperPlus<ProductScheduleInfo, ProductScheduleInfoVo> {

}
