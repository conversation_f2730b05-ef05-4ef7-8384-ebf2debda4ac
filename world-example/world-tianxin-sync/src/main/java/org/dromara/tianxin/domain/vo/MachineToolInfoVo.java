package org.dromara.tianxin.domain.vo;

import org.dromara.tianxin.domain.MachineToolInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等视图对象 machine_tool_info
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MachineToolInfo.class)
public class MachineToolInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 机床
     */
    @ExcelProperty(value = "机床")
    private String machineTool;

    /**
     * 资源
     */
    @ExcelProperty(value = "资源")
    private String resource;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long serialNo;

    /**
     * 可扫描
     */
    @ExcelProperty(value = "可扫描", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String isScannable;

    /**
     * 分组
     */
    @ExcelProperty(value = "分组")
    private String grouping;

    /**
     * 类别
     */
    @ExcelProperty(value = "类别")
    private String category;

    /**
     * 使用状态
     */
    @ExcelProperty(value = "使用状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_common_status")
    private String usageStatus;

    /**
     * 停用日期
     */
    @ExcelProperty(value = "停用日期")
    private Date deactivationDate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date operationTime;


}
