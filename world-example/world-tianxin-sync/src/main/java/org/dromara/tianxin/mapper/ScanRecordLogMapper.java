package org.dromara.tianxin.mapper;


import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.tianxin.domain.ScanRecordLog;
import org.dromara.tianxin.domain.vo.ScanRecordLogVo;

import java.util.List;

/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
public interface ScanRecordLogMapper extends BaseMapperPlus<ScanRecordLog, ScanRecordLogVo> {

    /**
     * 批量插入扫描记录日志
     *
     * @param scanRecordLogs 扫描记录日志列表
     * @return 插入结果
     */
    int insertBatchScanRecordLogs(List<ScanRecordLog> scanRecordLogs);

}
