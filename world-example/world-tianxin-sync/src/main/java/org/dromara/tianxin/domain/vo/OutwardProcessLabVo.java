package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.tianxin.domain.OutwardProcessLab;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等视图对象 outward_process_lab
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OutwardProcessLab.class)
public class OutwardProcessLabVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * PRT_SW
     */
    @ExcelProperty(value = "PRT_SW")
    private String prtSw;

    /**
     * PR号
     */
    @ExcelProperty(value = "PR号")
    private String prNo;

    /**
     * PR行
     */
    @ExcelProperty(value = "PR行")
    private String prItm;

    /**
     * 申请类型
     */
    @ExcelProperty(value = "申请类型")
    private String prType;

    /**
     * 品号
     */
    @ExcelProperty(value = "品号")
    private String prdNo;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String prdDesc;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Long qty;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String ut;

    /**
     * 请求日期
     */
    @ExcelProperty(value = "请求日期")
    private Date prDate;

    /**
     * PMC要求日期
     */
    @ExcelProperty(value = "PMC要求日期")
    private String pmcRequestDate;

    /**
     * 厂商代号
     */
    @ExcelProperty(value = "厂商代号")
    private String vendorCode;

    /**
     * 简称
     */
    @ExcelProperty(value = "简称")
    private String vendorSnm;

    /**
     * 厂商名
     */
    @ExcelProperty(value = "厂商名")
    private String vendorName;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private Long unitPrice;

    /**
     * 货币
     */
    @ExcelProperty(value = "货币")
    private String currency;

    /**
     * 是否供料
     */
    @ExcelProperty(value = "是否供料")
    private String isProvideMaterials;

    /**
     * 图号
     */
    @ExcelProperty(value = "图号")
    private String dwgNo;

    /**
     * PJ号
     */
    @ExcelProperty(value = "PJ号")
    private String pjNo;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNo;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String purchaseNum;

    /**
     * 批号
     */
    @ExcelProperty(value = "批号")
    private String batchNo;

    /**
     * 总价
     */
    @ExcelProperty(value = "总价")
    private Long sumPrice;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String sta;

    /**
     * 外发交期
     */
    @ExcelProperty(value = "外发交期")
    private Date outsourcingDate;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String custNo;

    /**
     * 工序号
     */
    @ExcelProperty(value = "工序号")
    private String zcNo;

    /**
     * 加工中心
     */
    @ExcelProperty(value = "加工中心")
    private String machiningCenter;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String zcName;

    /**
     * 电镀厂商
     */
    @ExcelProperty(value = "电镀厂商")
    private String epfSnm;

    /**
     * 电镀内容
     */
    @ExcelProperty(value = "电镀内容")
    private String electroplateContent;

    /**
     * 指定材料
     */
    @ExcelProperty(value = "指定材料")
    private String specifiedMaterials;

    /**
     * 是否电镀
     */
    @ExcelProperty(value = "是否电镀")
    private String isElectroplate;

    /**
     * 电镀厂商代号
     */
    @ExcelProperty(value = "电镀厂商代号")
    private String epfCode;

    /**
     * 电镀厂商名
     */
    @ExcelProperty(value = "电镀厂商名")
    private String epfName;

    /**
     * USR
     */
    @ExcelProperty(value = "USR")
    private String usr;

    /**
     * SYS_DATE
     */
    @ExcelProperty(value = "SYS_DATE")
    private Date sysDate;

    /**
     * host
     */
    @ExcelProperty(value = "host")
    private String host;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String rem;


}
