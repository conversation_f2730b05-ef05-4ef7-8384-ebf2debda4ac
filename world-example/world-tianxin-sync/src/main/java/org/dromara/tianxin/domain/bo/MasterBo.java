package org.dromara.tianxin.domain.bo;

import java.util.Date;

import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.tianxin.domain.Master;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PMC订单变更管理业务对象 master
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Master.class, reverseConvertGenerate = false)
public class MasterBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单状态
     */
    private String sta;

    /**
     * 备注信息
     */
    private String rem;

    /**
     * 接单日期
     */
    private Date orderDate;

    /**
     * 客户要求交期
     */
    private Date customerReqDeliveryDate;

    /**
     * 承诺交期
     */
    private Date promisedDeliveryDate;

    /**
     * 下计划日期
     */
    private Date nextPlanDate;

    /**
     * 要求完成日期
     */
    private Date requiredCompletionDate;

    /**
     * 末工序时间
     */
    private Date lastProcessTime;

    /**
     * 入仓日期
     */
    private Date warehouseEntryDate;

    /**
     * 实际交期
     */
    private Date actualDeliveryDate;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 项目号
     */
    private String projectNo;

    /**
     * 项目经理
     */
    private String projectManager;

    /**
     * 客户PO号
     */
    private String customerPoNo;

    /**
     * PO_ITM
     */
    private String poItm;

    /**
     * SO号
     */
    private String soNo;

    /**
     * SO项次
     */
    private String soItm;

    /**
     * MO_NO
     */
    private String moNo;

    /**
     * 上层订单号
     */
    private String parentOrderNo;

    /**
     * 零件分类
     */
    private String partClassification;

    /**
     * 品号
     */
    private String prdNo;

    /**
     * 图号
     */
    private String dwgNo;

    /**
     * 版本
     */
    private String version;

    /**
     * 客户产品料号
     */
    private String customerProductNo;

    /**
     * 客户产品名称
     */
    private String customerProductName;

    /**
     * 产品描述
     */
    private String productDescription;

    /**
     * 订单数量
     */
    private Long orderQuantity;

    /**
     * 生产数量
     */
    private Long productionQuantity;

    /**
     * 工艺数量
     */
    private Long processQuantity;

    /**
     * 报废数量
     */
    private Long scrapQuantity;

    /**
     * 已入仓数
     */
    private Long inWarehouseCount;

    /**
     * 欠入仓数
     */
    private Long outstandingWarehouseCount;

    /**
     * 交货数量
     */
    private Long deliveryQuantity;

    /**
     * 欠出货数量
     */
    private Long outstandingShipmentQuantity;

    /**
     * 单件净重
     */
    private Long singlePieceNetWeight;

    /**
     * 单位
     */
    private String unit;

    /**
     * 行业
     */
    private String industry;

    /**
     * 订单类别
     */
    private String orderCategory;

    /**
     * 计划类型
     */
    private String planType;

    /**
     * 下单员
     */
    private String orderPlacer;

    /**
     * 跟单员
     */
    private String followUpStaff;

    /**
     * BOM负责人
     */
    private String bomManager;

    /**
     * 工艺编制人
     */
    private String processCompiler;

    /**
     * 合单标识
     */
    private String mergeFlag;

    /**
     * 急单标识
     */
    private String urgentFlag;

    /**
     * 打样标识
     */
    private String sampleFlag;

    /**
     * 是否有标准件
     */
    private String hasStandardParts;

    /**
     * 是否买料
     */
    private String isMaterialPurchased;

    /**
     * 是否电镀
     */
    private String isElectroplated;

    /**
     * 动作
     */
    private String action;

    /**
     * 当前工序
     */
    private String currentProcess;

    /**
     * 加工材料
     */
    private String processingMaterial;

    /**
     * 要求材料
     */
    private String requiredMaterial;

    /**
     * 报价材料
     */
    private String quotedMaterial;

    /**
     * 图纸难度等级
     */
    private String drawingDifficultyLevel;

    /**
     * 工艺难度等级
     */
    private String processDifficultyLevel;

    /**
     * 工艺版本
     */
    private String processVersion;

    /**
     * 完成部门 (供应商)
     */
    private String completionDepartment;

    /**
     * 生产车间 (加工车间)
     */
    private String productionWorkshop;

    /**
     * 利润中心
     */
    private String profitCenter;

    /**
     * 成本中心
     */
    private String costCenter;

    /**
     * 收费备注
     */
    private String chargeRemarks;

    /**
     * 关单原因
     */
    private String closeOrderReason;

    /**
     * 状态1
     */
    private Long sta1;

    /**
     * SO单 (同步标记)
     */
    private String soDocument;

    /**
     * MO号 (同步标记)
     */
    private String moDocument;

    /**
     * PMC分组 (同步标记)
     */
    private String pmcGroup;

    /**
     * 工艺 (同步标记)
     */
    private String process;

    /**
     * 排产 (同步标记)
     */
    private String productionScheduling;

    /**
     * MRP (同步标记)
     */
    private String mrp;

    /**
     * 请购 (同步标记)
     */
    private String purchaseRequest;

    /**
     * 下采购单 (同步标记)
     */
    private String purchaseOrder;

    /**
     * 材料收货 (同步标记)
     */
    private String materialReceipt;

    /**
     * IQC检测 (同步标记)
     */
    private String iqcInspection;

    /**
     * 材料退货 (同步标记)
     */
    private String materialReturn;

    /**
     * 材料进仓 (同步标记)
     */
    private String materialInbound;

    /**
     * 托工 (同步标记)
     */
    private String subcontracting;

    /**
     * 请购单号 (同步天心单号)
     */
    private String purchaseRequestNo;

    /**
     * 采购单号 (同步天心单号)
     */
    private String purchaseOrderNo;

    /**
     * 入库单号 (同步天心单号)
     */
    private String warehouseEntryNo;

    /**
     * 出库单号 (同步天心单号)
     */
    private String warehouseExitNo;

    /**
     * 销货单号 (同步天心单号)
     */
    private String salesOrderNo;

    /**
     * 完工状态
     */
    private String finishStatus;
}
