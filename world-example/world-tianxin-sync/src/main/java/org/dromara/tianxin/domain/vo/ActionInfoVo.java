package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.tianxin.domain.ActionInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 动作信息对象 action_info
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ActionInfo.class)
public class ActionInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 动作
     */
    @ExcelProperty(value = "动作", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String action;

    /**
     * 部门
     */
    @ExcelProperty(value = "部门")
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "createDept")
    private String department;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_common_status")
    private String status;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long sequenceNo;

    /**
     * 条码号
     */
    @ExcelProperty(value = "条码号")
    private String barcodeNo;

    /**
     * 是否控制数量
     */
    @ExcelProperty(value = "是否控制数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long isQuantityControlled;

    /**
     * 是否输入通过数量
     */
    @ExcelProperty(value = "是否输入通过数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long isPassedQuantity;

    /**
     * 是否输入报废数量
     */
    @ExcelProperty(value = "是否输入报废数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long isScrappedQuantity;

    /**
     * 是否自带上工序数量
     */
    @ExcelProperty(value = "是否自带上工序数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long isPreviousProcess;

    /**
     * 是否跳工序
     */
    @ExcelProperty(value = "是否跳工序", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long allowSkipProcess;

    /**
     * 是否输入数量
     */
    @ExcelProperty(value = "是否输入数量", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Long noQuantity;

    /**
     * 操作人工号
     */
    @ExcelProperty(value = "操作人工号")
    private String operatorCode;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String operatorName;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date operationTime;

    /**
     * 部门
     */
    private Long createDept;
}
