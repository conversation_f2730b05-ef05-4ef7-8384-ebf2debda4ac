package org.dromara.tianxin.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.Master;
import org.dromara.tianxin.domain.OutwardProcessLab;
import org.dromara.tianxin.domain.bo.OutwardProcessLabBo;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.mapper.MasterMapper;
import org.dromara.tianxin.mapper.OutwardProcessLabMapper;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OutwardProcessLabServiceImpl implements IOutwardProcessLabService {

    private final OutwardProcessLabMapper baseMapper;

    @Resource
    private MasterMapper masterMapper;

    /**
     * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param id 主键
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @Override
    public OutwardProcessLabVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等分页列表
     */
    @Override
    public TableDataInfo<OutwardProcessLabVo> queryPageList(OutwardProcessLabBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OutwardProcessLab> lqw = buildQueryWrapper(bo);
        Page<OutwardProcessLabVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     *
     * @param bo 查询条件
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @Override
    public List<OutwardProcessLabVo> queryList(OutwardProcessLabBo bo) {
        LambdaQueryWrapper<OutwardProcessLab> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OutwardProcessLab> buildQueryWrapper(OutwardProcessLabBo bo) {
        LambdaQueryWrapper<OutwardProcessLab> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OutwardProcessLab::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getPrtSw()), OutwardProcessLab::getPrtSw, bo.getPrtSw());
        lqw.eq(StringUtils.isNotBlank(bo.getPrNo()), OutwardProcessLab::getPrNo, bo.getPrNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPrItm()), OutwardProcessLab::getPrItm, bo.getPrItm());
        lqw.eq(StringUtils.isNotBlank(bo.getPrType()), OutwardProcessLab::getPrType, bo.getPrType());
        lqw.eq(StringUtils.isNotBlank(bo.getPrdNo()), OutwardProcessLab::getPrdNo, bo.getPrdNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPrdDesc()), OutwardProcessLab::getPrdDesc, bo.getPrdDesc());
        lqw.eq(bo.getQty() != null, OutwardProcessLab::getQty, bo.getQty());
        lqw.eq(StringUtils.isNotBlank(bo.getUt()), OutwardProcessLab::getUt, bo.getUt());
        lqw.eq(bo.getPrDate() != null, OutwardProcessLab::getPrDate, bo.getPrDate());
        lqw.eq(StringUtils.isNotBlank(bo.getPmcRequestDate()), OutwardProcessLab::getPmcRequestDate,
            bo.getPmcRequestDate());
        lqw.eq(StringUtils.isNotBlank(bo.getVendorCode()), OutwardProcessLab::getVendorCode, bo.getVendorCode());
        lqw.eq(StringUtils.isNotBlank(bo.getVendorSnm()), OutwardProcessLab::getVendorSnm, bo.getVendorSnm());
        lqw.like(StringUtils.isNotBlank(bo.getVendorName()), OutwardProcessLab::getVendorName, bo.getVendorName());
        lqw.eq(bo.getUnitPrice() != null, OutwardProcessLab::getUnitPrice, bo.getUnitPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), OutwardProcessLab::getCurrency, bo.getCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getIsProvideMaterials()), OutwardProcessLab::getIsProvideMaterials,
            bo.getIsProvideMaterials());
        lqw.eq(StringUtils.isNotBlank(bo.getDwgNo()), OutwardProcessLab::getDwgNo, bo.getDwgNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPjNo()), OutwardProcessLab::getPjNo, bo.getPjNo());
        if (StringUtils.isNotBlank(bo.getMoNo())) {
            List<String> moList =
                bo.getMoNo().lines().map(String::trim).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            lqw.in(OutwardProcessLab::getMoNo, moList);
        }

        lqw.eq(StringUtils.isNotBlank(bo.getPurchaseNum()), OutwardProcessLab::getPurchaseNum, bo.getPurchaseNum());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchNo()), OutwardProcessLab::getBatchNo, bo.getBatchNo());
        lqw.eq(bo.getSumPrice() != null, OutwardProcessLab::getSumPrice, bo.getSumPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getSta()), OutwardProcessLab::getSta, bo.getSta());
        lqw.eq(bo.getOutsourcingDate() != null, OutwardProcessLab::getOutsourcingDate, bo.getOutsourcingDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCustNo()), OutwardProcessLab::getCustNo, bo.getCustNo());
        lqw.eq(StringUtils.isNotBlank(bo.getZcNo()), OutwardProcessLab::getZcNo, bo.getZcNo());
        lqw.eq(StringUtils.isNotBlank(bo.getMachiningCenter()), OutwardProcessLab::getMachiningCenter,
            bo.getMachiningCenter());
        lqw.like(StringUtils.isNotBlank(bo.getZcName()), OutwardProcessLab::getZcName, bo.getZcName());
        lqw.eq(StringUtils.isNotBlank(bo.getEpfSnm()), OutwardProcessLab::getEpfSnm, bo.getEpfSnm());
        lqw.eq(StringUtils.isNotBlank(bo.getElectroplateContent()), OutwardProcessLab::getElectroplateContent,
            bo.getElectroplateContent());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecifiedMaterials()), OutwardProcessLab::getSpecifiedMaterials,
            bo.getSpecifiedMaterials());
        lqw.eq(StringUtils.isNotBlank(bo.getIsElectroplate()), OutwardProcessLab::getIsElectroplate,
            bo.getIsElectroplate());
        lqw.eq(StringUtils.isNotBlank(bo.getEpfCode()), OutwardProcessLab::getEpfCode, bo.getEpfCode());
        lqw.like(StringUtils.isNotBlank(bo.getEpfName()), OutwardProcessLab::getEpfName, bo.getEpfName());
        lqw.eq(StringUtils.isNotBlank(bo.getUsr()), OutwardProcessLab::getUsr, bo.getUsr());
        lqw.eq(bo.getSysDate() != null, OutwardProcessLab::getSysDate, bo.getSysDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHost()), OutwardProcessLab::getHost, bo.getHost());
        lqw.eq(StringUtils.isNotBlank(bo.getRem()), OutwardProcessLab::getRem, bo.getRem());

        // 登图日期
        Map<String, Object> params = bo.getParams();
        String beginSysDate = (String)params.get("beginSysDate");
        String endSysDate = (String)params.get("endSysDate");
        if (StringUtils.isNotBlank(beginSysDate) && StringUtils.isNotBlank(endSysDate)) {
            // 解析为 LocalDateTime，end +1 天，用 lt（<）
            LocalDateTime begin = LocalDateTime.parse(beginSysDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end =
                LocalDateTime.parse(endSysDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).plusDays(1); // 加一天
            lqw.ge(OutwardProcessLab::getSysDate, begin).lt(OutwardProcessLab::getSysDate, end); // < end
        }
        return lqw;
    }

    /**
     * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param bo 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OutwardProcessLabBo bo) {
        OutwardProcessLab add = MapstructUtils.convert(bo, OutwardProcessLab.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param bo 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OutwardProcessLabBo bo) {
        OutwardProcessLab update = MapstructUtils.convert(bo, OutwardProcessLab.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OutwardProcessLab entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等信息
     *
     * @param ids 待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取外发标签列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<OutsourceVo> getOutSourceList(OutwardProcessLabBo bo) {
        List<OutsourceVo> result = new ArrayList<>();
        LambdaQueryWrapper<OutwardProcessLab> lqw = buildQueryWrapper(bo);
        List<OutwardProcessLabVo> outwardProcessLabVos = baseMapper.selectVoList(lqw);
        if (ObjectUtils.isEmpty(outwardProcessLabVos)) {
            return result;
        }
        // 获取MO列表
        List<String> moNos = outwardProcessLabVos.stream().map(OutwardProcessLabVo::getMoNo).distinct().toList();
        // 根据mo获取mater信息
        LambdaQueryWrapper<Master> masterLqw = new LambdaQueryWrapper<>();
        masterLqw.in(Master::getMoNo, moNos);
        List<Master> masters = masterMapper.selectList(masterLqw);
        for (OutwardProcessLabVo outwardProcessLabVo : outwardProcessLabVos) {
            OutsourceVo outsourceVo = new OutsourceVo();
            outsourceVo.setId(outwardProcessLabVo.getId());
            outsourceVo.setMoNo(outwardProcessLabVo.getMoNo());
            outsourceVo.setVendorSnm(outwardProcessLabVo.getVendorSnm());
            Master master = masters.stream().filter(ma -> ma.getMoNo().equals(outwardProcessLabVo.getMoNo()))
                .findFirst().orElse(null);
            if (master != null) {
                outsourceVo.setDwgNo(master.getDwgNo());
                outsourceVo.setCustomerCode(master.getCustomerCode());
                outsourceVo.setPoType(master.getPoItm());
                outsourceVo.setOrderQuantity(master.getOrderQuantity());
                outsourceVo.setProcessContent(master.getCurrentProcess());
                outsourceVo.setPartClassification(master.getPartClassification());
            }
            result.add(outsourceVo);
        }
        return result;
    }
}
