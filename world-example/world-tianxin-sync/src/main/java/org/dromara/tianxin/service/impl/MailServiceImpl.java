package org.dromara.tianxin.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.tianxin.domain.dto.MailSendDto;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.service.IMailService;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 邮件服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MailServiceImpl implements IMailService {

    private final IOutwardProcessLabService outwardProcessLabService;

    @Override
    public String sendMail(MailSendDto mailSendDto) {
        try {
            // 构建邮件内容
            String emailContent = buildEmailContent(mailSendDto);
            
            // 发送邮件
            if (mailSendDto.getIsHtml()) {
                MailUtils.send(
                    mailSendDto.getTo(),
                    mailSendDto.getCc(),
                    mailSendDto.getBcc(),
                    mailSendDto.getSubject(),
                    emailContent,
                    true
                );
            } else {
                MailUtils.send(
                    mailSendDto.getTo(),
                    mailSendDto.getCc(),
                    mailSendDto.getBcc(),
                    mailSendDto.getSubject(),
                    emailContent,
                    false
                );
            }
            
            log.info("邮件发送成功，收件人：{}，主题：{}", mailSendDto.getTo(), mailSendDto.getSubject());
            return "邮件发送成功";
        } catch (Exception e) {
            log.error("邮件发送失败，收件人：{}，主题：{}，错误：{}", 
                mailSendDto.getTo(), mailSendDto.getSubject(), e.getMessage(), e);
            throw new ServiceException("邮件发送失败：" + e.getMessage());
        }
    }

    @Override
    public String sendProcessLabNotification(String to, List<Long> processLabIds) {
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo(to);
        mailSendDto.setSubject("外发加工标签通知");
        mailSendDto.setContent(buildProcessLabNotificationContent());
        mailSendDto.setIsHtml(true);
        mailSendDto.setProcessLabIds(processLabIds);
        
        return sendMail(mailSendDto);
    }

    @Override
    public String sendUrgentProcessLabNotification(String to, List<Long> processLabIds) {
        MailSendDto mailSendDto = new MailSendDto();
        mailSendDto.setTo(to);
        mailSendDto.setSubject("【紧急】外发加工标签通知");
        mailSendDto.setContent(buildUrgentProcessLabNotificationContent());
        mailSendDto.setIsHtml(true);
        mailSendDto.setProcessLabIds(processLabIds);
        
        return sendMail(mailSendDto);
    }

    /**
     * 构建邮件内容
     *
     * @param mailSendDto 邮件发送参数
     * @return 邮件内容
     */
    private String buildEmailContent(MailSendDto mailSendDto) {
        StringBuilder content = new StringBuilder();
        
        // 添加基础内容
        content.append(mailSendDto.getContent());
        
        // 如果包含外发加工标签ID，则添加相关数据
        if (mailSendDto.getProcessLabIds() != null && !mailSendDto.getProcessLabIds().isEmpty()) {
            if (mailSendDto.getIsHtml()) {
                content.append("<br><br><h3>外发加工标签信息</h3>");
            } else {
                content.append("\n\n=== 外发加工标签信息 ===\n");
            }
            
            for (Long id : mailSendDto.getProcessLabIds()) {
                try {
                    OutwardProcessLabVo processLab = outwardProcessLabService.queryById(id);
                    if (processLab != null) {
                        if (mailSendDto.getIsHtml()) {
                            content.append(buildHtmlProcessLabInfo(processLab));
                        } else {
                            content.append(buildTextProcessLabInfo(processLab));
                        }
                    }
                } catch (Exception e) {
                    log.warn("获取外发加工标签信息失败，ID：{}，错误：{}", id, e.getMessage());
                }
            }
        }
        
        return content.toString();
    }

    /**
     * 构建HTML格式的外发加工标签信息
     *
     * @param processLab 外发加工标签信息
     * @return HTML格式的信息
     */
    private String buildHtmlProcessLabInfo(OutwardProcessLabVo processLab) {
        return String.format(
            "<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; background-color: #f9f9f9;'>" +
            "<h4 style='color: #2c5aa0; margin-top: 0;'>外发加工标签 - ID: %d</h4>" +
            "<table style='width: 100%%; border-collapse: collapse;'>" +
            "<tr><td style='padding: 5px; font-weight: bold; width: 120px;'>PR号:</td><td style='padding: 5px;'>%s</td></tr>" +
            "<tr><td style='padding: 5px; font-weight: bold;'>品号:</td><td style='padding: 5px;'>%s</td></tr>" +
            "<tr><td style='padding: 5px; font-weight: bold;'>描述:</td><td style='padding: 5px;'>%s</td></tr>" +
            "<tr><td style='padding: 5px; font-weight: bold;'>数量:</td><td style='padding: 5px;'>%s %s</td></tr>" +
            "<tr><td style='padding: 5px; font-weight: bold;'>厂商:</td><td style='padding: 5px;'>%s (%s)</td></tr>" +
            "<tr><td style='padding: 5px; font-weight: bold;'>状态:</td><td style='padding: 5px;'>%s</td></tr>" +
            "<tr><td style='padding: 5px; font-weight: bold;'>外发交期:</td><td style='padding: 5px;'>%s</td></tr>" +
            "</table>" +
            "</div>",
            processLab.getId(),
            StringUtils.blankToDefault(processLab.getPrNo(), "-"),
            StringUtils.blankToDefault(processLab.getPrdNo(), "-"),
            StringUtils.blankToDefault(processLab.getPrdDesc(), "-"),
            processLab.getQty() != null ? processLab.getQty() : "-",
            StringUtils.blankToDefault(processLab.getUt(), ""),
            StringUtils.blankToDefault(processLab.getVendorName(), "-"),
            StringUtils.blankToDefault(processLab.getVendorCode(), "-"),
            StringUtils.blankToDefault(processLab.getSta(), "-"),
            processLab.getOutsourcingDate() != null ? processLab.getOutsourcingDate().toString() : "-"
        );
    }

    /**
     * 构建文本格式的外发加工标签信息
     *
     * @param processLab 外发加工标签信息
     * @return 文本格式的信息
     */
    private String buildTextProcessLabInfo(OutwardProcessLabVo processLab) {
        return String.format(
            "\n--- 外发加工标签 - ID: %d ---\n" +
            "PR号: %s\n" +
            "品号: %s\n" +
            "描述: %s\n" +
            "数量: %s %s\n" +
            "厂商: %s (%s)\n" +
            "状态: %s\n" +
            "外发交期: %s\n" +
            "--------------------------------\n",
            processLab.getId(),
            StringUtils.blankToDefault(processLab.getPrNo(), "-"),
            StringUtils.blankToDefault(processLab.getPrdNo(), "-"),
            StringUtils.blankToDefault(processLab.getPrdDesc(), "-"),
            processLab.getQty() != null ? processLab.getQty() : "-",
            StringUtils.blankToDefault(processLab.getUt(), ""),
            StringUtils.blankToDefault(processLab.getVendorName(), "-"),
            StringUtils.blankToDefault(processLab.getVendorCode(), "-"),
            StringUtils.blankToDefault(processLab.getSta(), "-"),
            processLab.getOutsourcingDate() != null ? processLab.getOutsourcingDate().toString() : "-"
        );
    }

    /**
     * 构建外发加工标签通知内容
     *
     * @return 通知内容
     */
    private String buildProcessLabNotificationContent() {
        return "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>" +
               "<h2 style='color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;'>外发加工任务通知</h2>" +
               "<p>尊敬的合作伙伴，</p>" +
               "<p>您有新的外发加工任务需要处理，详细信息如下：</p>" +
               "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>" +
               "<p><strong>注意事项：</strong></p>" +
               "<ul>" +
               "<li>请严格按照技术要求进行加工</li>" +
               "<li>如有疑问请及时联系我们</li>" +
               "<li>请按时完成并交付</li>" +
               "</ul>" +
               "</div>";
    }

    /**
     * 构建紧急外发加工标签通知内容
     *
     * @return 紧急通知内容
     */
    private String buildUrgentProcessLabNotificationContent() {
        return "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>" +
               "<div style='background-color: #ffebee; border-left: 4px solid #f44336; padding: 15px; margin-bottom: 20px;'>" +
               "<h2 style='color: #d32f2f; margin-top: 0;'>⚠️ 紧急任务通知</h2>" +
               "</div>" +
               "<p>尊敬的合作伙伴，</p>" +
               "<p><strong style='color: #d32f2f;'>您有紧急的外发加工任务需要处理，请优先安排：</strong></p>" +
               "<div style='background-color: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #ffb74d;'>" +
               "<p><strong>紧急处理要求：</strong></p>" +
               "<ul>" +
               "<li><strong>请立即安排生产</strong></li>" +
               "<li>严格按照技术要求进行加工</li>" +
               "<li>如有任何问题请立即联系我们</li>" +
               "<li>请及时反馈进度</li>" +
               "</ul>" +
               "</div>";
    }
}
