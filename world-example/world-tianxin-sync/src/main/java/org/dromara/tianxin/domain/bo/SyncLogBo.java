package org.dromara.tianxin.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.tianxin.domain.SyncLog;

/**
 * 天心天思数据同步日志业务对象
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SyncLog.class, reverseConvertGenerate = false)
public class SyncLogBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 同步模块
     */
    @NotBlank(message = "同步模块不能为空", groups = {AddGroup.class, EditGroup.class})
    private String module;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String dataType;

    /**
     * 数据ID
     */
    @NotBlank(message = "数据ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String dataId;

    /**
     * 同步状态：0-失败，1-成功
     */
    @NotNull(message = "同步状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer syncStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 备注信息
     */
    private String remark;
}
