package org.dromara.tianxin.domain.bo;

import org.dromara.tianxin.domain.ActionInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 动作信息业务对象 action_info
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ActionInfo.class, reverseConvertGenerate = false)
public class ActionInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 动作
     */
    private String action;

    /**
     * 状态
     */
    private String status;

    /**
     * 序号
     */
    private Long sequenceNo;

    /**
     * 条码号
     */
    private String barcodeNo;

    /**
     * 是否控制数量
     */
    private Long isQuantityControlled;

    /**
     * 是否输入通过数量
     */
    private Long isPassedQuantity;

    /**
     * 是否输入报废数量
     */
    private Long isScrappedQuantity;

    /**
     * 是否自带上工序数量
     */
    private Long isPreviousProcess;

    /**
     * 是否跳工序
     */
    private Long allowSkipProcess;

    /**
     * 是否输入数量
     */
    private Long noQuantity;

    /**
     * 操作人工号
     */
    private String operatorCode;

    /**
     * 姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;


}
