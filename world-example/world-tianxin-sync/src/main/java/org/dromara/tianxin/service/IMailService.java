package org.dromara.tianxin.service;

import org.dromara.tianxin.domain.dto.MailSendDto;

/**
 * 邮件服务接口
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface IMailService {

    /**
     * 发送邮件
     *
     * @param mailSendDto 邮件发送参数
     * @return 发送结果消息
     */
    String sendMail(MailSendDto mailSendDto);

    /**
     * 发送外发加工标签通知邮件
     *
     * @param to 收件人
     * @param processLabIds 外发加工标签ID列表
     * @return 发送结果消息
     */
    String sendProcessLabNotification(String to, java.util.List<Long> processLabIds);

    /**
     * 发送紧急外发加工通知邮件
     *
     * @param to 收件人
     * @param processLabIds 外发加工标签ID列表
     * @return 发送结果消息
     */
    String sendUrgentProcessLabNotification(String to, java.util.List<Long> processLabIds);
}
