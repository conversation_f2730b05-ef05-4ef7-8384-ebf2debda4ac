package org.dromara.tianxin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteDeptService;
import org.dromara.tianxin.domain.ActionInfo;
import org.dromara.tianxin.domain.bo.ActionInfoBo;
import org.dromara.tianxin.domain.vo.ActionInfoVo;
import org.dromara.tianxin.domain.vo.OrganizationActionVo;
import org.dromara.tianxin.mapper.ActionInfoMapper;
import org.dromara.tianxin.service.IActionInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 动作信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ActionInfoServiceImpl implements IActionInfoService {

    private final ActionInfoMapper baseMapper;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    /**
     * 查询动作信息
     *
     * @param id 主键
     * @return 动作信息
     */
    @Override
    public ActionInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询动作信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 动作信息分页列表
     */
    @Override
    public TableDataInfo<ActionInfoVo> queryPageList(ActionInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ActionInfo> lqw = buildQueryWrapper(bo);
        Page<ActionInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的动作信息列表
     *
     * @param bo 查询条件
     * @return 动作信息列表
     */
    @Override
    public List<ActionInfoVo> queryList(ActionInfoBo bo) {
        LambdaQueryWrapper<ActionInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ActionInfo> buildQueryWrapper(ActionInfoBo bo) {
        LambdaQueryWrapper<ActionInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ActionInfo::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getAction()), ActionInfo::getAction, bo.getAction());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatorCode()), ActionInfo::getOperatorCode, bo.getOperatorCode());
        lqw.like(StringUtils.isNotBlank(bo.getOperatorName()), ActionInfo::getOperatorName, bo.getOperatorName());
        lqw.eq(bo.getOperationTime() != null, ActionInfo::getOperationTime, bo.getOperationTime());
        return lqw;
    }

    /**
     * 新增动作信息
     *
     * @param bo 动作信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ActionInfoBo bo) {
        ActionInfo add = MapstructUtils.convert(bo, ActionInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改动作信息
     *
     * @param bo 动作信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ActionInfoBo bo) {
        ActionInfo update = MapstructUtils.convert(bo, ActionInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ActionInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除动作信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据当前用户组织ID获取组织及其下属动作信息
     *
     * @return 组织动作信息
     */
    @Override
    public OrganizationActionVo getOrganizationActionsByCurrentUser() {
        // 获取当前用户的部门ID
        Long deptId = LoginHelper.getDeptId();
        String deptName = LoginHelper.getDeptName();

        if (deptId == null) {
            log.warn("当前用户未关联部门，无法获取组织动作信息");
            return new OrganizationActionVo(null, "未知部门", List.of());
        }

        try {
            // 通过Dubbo服务获取当前部门及其下属部门的ID列表
            List<Long> deptIds = remoteDeptService.getDeptIdsIncludingChildren(deptId);

            if (deptIds.isEmpty()) {
                log.warn("未找到部门信息，部门ID: {}, 部门名称: {}", deptId, deptName);
                return new OrganizationActionVo(deptId, deptName, List.of());
            }

            // 根据部门ID列表获取动作信息（查询create_dept字段）
            List<ActionInfoVo> actionList = baseMapper.selectActionInfosByDeptIds(deptIds);

            log.info("获取组织动作信息 - 部门ID: {}, 部门名称: {}, 涉及部门ID: {}, 动作数量: {}",
                deptId, deptName, deptIds, actionList.size());

            return new OrganizationActionVo(deptId, deptName, actionList);

        } catch (Exception e) {
            log.error("获取组织动作信息失败，部门ID: {}, 部门名称: {}", deptId, deptName, e);
            return new OrganizationActionVo(deptId, deptName, List.of());
        }
    }
}
