package org.dromara.tianxin.domain;

import java.io.Serial;
import java.util.Date;

import org.dromara.common.tenant.core.TenantEntity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息对象 scan_record_log
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("scan_record_log")
public class ScanRecordLog extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * MO号
     */
    private String moNo;

    /**
     * 工序号
     */
    private Long processNo;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 姓名
     */
    private String operatorName;

    /**
     * 机台号
     */
    private String machineNo;

    /**
     * 动作
     */
    private String action;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 通过数量
     */
    private Long passedQuantity;

    /**
     * 报废数量
     */
    private Long scrappedQuantity;

    /**
     * 总进度
     */
    private Long totalProgress;

    /**
     * 当前进度
     */
    private Long currentProgress;

    /**
     * 工艺工时
     */
    private Long processManHours;

    /**
     * 实际时间
     */
    private Long actualTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 储位
     */
    private String storageLocation;

    /**
     * 品号
     */
    private String partNo;

    /**
     * 图号
     */
    private String drawingNo;

    /**
     * 版本
     */
    @Version
    private String version;

    /**
     * 物料描述
     */
    private String materialDescription;

    /**
     * 客户产品料号
     */
    private String customerProductNo;

    /**
     * 客户产品名称
     */
    private String customerProductName;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 电脑名
     */
    private String computerName;

    /**
     * 行业
     */
    private String industry;

    /**
     * 排产工时
     */
    private Long productionScheduleManHours;

    /**
     * 创建时间
     */
    private Date creationTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Long delFlag;

}
