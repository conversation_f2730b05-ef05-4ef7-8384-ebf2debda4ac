package org.dromara.tianxin.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 动作信息对象 action_info
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("action_info")
public class ActionInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 动作
     */
    private String action;


    /**
     * 状态
     */
    private String status;

    /**
     * 序号
     */
    private Long sequenceNo;

    /**
     * 条码号
     */
    private String barcodeNo;

    /**
     * 是否控制数量
     */
    private Long isQuantityControlled;

    /**
     * 是否输入通过数量
     */
    private Long isPassedQuantity;

    /**
     * 是否输入报废数量
     */
    private Long isScrappedQuantity;

    /**
     * 是否自带上工序数量
     */
    private Long isPreviousProcess;

    /**
     * 是否跳工序
     */
    private Long allowSkipProcess;

    /**
     * 是否输入数量
     */
    private Long noQuantity;

    /**
     * 操作人工号
     */
    private String operatorCode;

    /**
     * 姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Long delFlag;


}
