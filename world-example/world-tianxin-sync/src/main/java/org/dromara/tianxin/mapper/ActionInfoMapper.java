package org.dromara.tianxin.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.tianxin.domain.ActionInfo;
import org.dromara.tianxin.domain.vo.ActionInfoVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 动作信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
public interface ActionInfoMapper extends BaseMapperPlus<ActionInfo, ActionInfoVo> {
    
    /**
     * 根据部门ID列表获取动作信息列表
     *
     * @param deptIds 部门ID列表
     * @return 动作信息列表
     */
    List<ActionInfoVo> selectActionInfosByDeptIds(@Param("deptIds") List<Long> deptIds);
}
