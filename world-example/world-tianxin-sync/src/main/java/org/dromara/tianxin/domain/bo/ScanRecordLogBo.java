package org.dromara.tianxin.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.tianxin.domain.ScanRecordLog;

/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息业务对象 scan_record_log
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ScanRecordLog.class, reverseConvertGenerate = false)
public class ScanRecordLogBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * MO号
     */
    private String moNo;

    /**
     * 工序号
     */
    private Long processNo;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 姓名
     */
    private String operatorName;

    /**
     * 机台号
     */
    private String machineNo;

    /**
     * 动作
     */
    private String action;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 通过数量
     */
    private Long passedQuantity;

    /**
     * 报废数量
     */
    private Long scrappedQuantity;

    /**
     * 总进度
     */
    private Long totalProgress;

    /**
     * 当前进度
     */
    private Long currentProgress;

    /**
     * 工艺工时
     */
    private Long processManHours;

    /**
     * 实际时间
     */
    private Long actualTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客户代码
     */
    private String customerCode;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 储位
     */
    private String storageLocation;

    /**
     * 品号
     */
    private String partNo;

    /**
     * 图号
     */
    private String drawingNo;

    /**
     * 物料描述
     */
    private String materialDescription;

    /**
     * 客户产品料号
     */
    private String customerProductNo;

    /**
     * 客户产品名称
     */
    private String customerProductName;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 电脑名
     */
    private String computerName;

    /**
     * 行业
     */
    private String industry;

    /**
     * 排产工时
     */
    private Long productionScheduleManHours;

    /**
     * 创建时间
     */
    private Date creationTime;


}
