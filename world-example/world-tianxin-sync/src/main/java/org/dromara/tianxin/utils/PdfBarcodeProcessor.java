package org.dromara.tianxin.utils;

import com.itextpdf.kernel.pdf.*;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import com.itextpdf.io.image.ImageDataFactory;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.BarcodeGenerator;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * PDF条形码处理器
 * 使用iText库将条形码合并到PDF右上角
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class PdfBarcodeProcessor {

    /**
     * 条形码距离右边缘的距离（点）
     */
    private static final float BARCODE_RIGHT_MARGIN = 20.0f;
    
    /**
     * 条形码距离上边缘的距离（点）
     */
    private static final float BARCODE_TOP_MARGIN = 20.0f;
    
    /**
     * 条形码宽度（点）
     */
    private static final float BARCODE_WIDTH = 250.0f;
    
    /**
     * 条形码高度（点）
     */
    private static final float BARCODE_HEIGHT = 50.0f;

    /**
     * 处理PDF文件，在右上角添加条形码（使用默认位置和尺寸）
     *
     * @param pdfBytes 原始PDF字节数组
     * @param fileName 文件名（用于生成条形码内容）
     * @return 处理后的PDF字节数组
     * @throws Exception 处理异常
     */
    public static byte[] addBarcodeToPdf(byte[] pdfBytes, String fileName) throws Exception {
        return addBarcodeToPdf(pdfBytes, fileName, 
                (int) BARCODE_RIGHT_MARGIN, (int) BARCODE_TOP_MARGIN, 
                (int) BARCODE_WIDTH, (int) BARCODE_HEIGHT);
    }

    /**
     * 处理PDF文件，在指定位置添加条形码
     *
     * @param pdfBytes 原始PDF字节数组
     * @param fileName 文件名（用于生成条形码内容）
     * @param barcodeX 条形码距离右边缘的距离（像素）
     * @param barcodeY 条形码距离上边缘的距离（像素）
     * @param barcodeWidth 条形码宽度（像素）
     * @param barcodeHeight 条形码高度（像素）
     * @return 处理后的PDF字节数组
     * @throws Exception 处理异常
     */
    public static byte[] addBarcodeToPdf(byte[] pdfBytes, String fileName, 
                                        int barcodeX, int barcodeY, 
                                        int barcodeWidth, int barcodeHeight) throws Exception {
        return addBarcodeToPdf(pdfBytes, fileName, "right", barcodeX, barcodeY, barcodeWidth, barcodeHeight);
    }

    /**
     * 处理PDF文件，在指定位置添加条形码
     *
     * @param pdfBytes 原始PDF字节数组
     * @param fileName 文件名（用于生成条形码内容）
     * @param barcodeHorizontal 条形码水平位置（left/center/right）
     * @param barcodeX 条形码距离边缘的距离（像素）
     * @param barcodeY 条形码距离上边缘的距离（像素）
     * @param barcodeWidth 条形码宽度（像素）
     * @param barcodeHeight 条形码高度（像素）
     * @return 处理后的PDF字节数组
     * @throws Exception 处理异常
     */
    public static byte[] addBarcodeToPdf(byte[] pdfBytes, String fileName, 
                                        String barcodeHorizontal, int barcodeX, int barcodeY, 
                                        int barcodeWidth, int barcodeHeight) throws Exception {
        if (pdfBytes == null || pdfBytes.length == 0) {
            throw new IllegalArgumentException("PDF文件内容不能为空");
        }
        
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        log.info("开始处理PDF文件: {}, 大小: {} bytes, 条形码位置: 水平={}, 边距={}px, 距离上边缘{}px, 尺寸: {}x{}px", 
                fileName, pdfBytes.length, barcodeHorizontal, barcodeX, barcodeY, barcodeWidth, barcodeHeight);

        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 读取PDF文档
            PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream), new PdfWriter(outputStream));
            Document document = new Document(pdfDoc);

            // 生成条形码
            byte[] barcodeBytes = BarcodeGenerator.generateCode128A(fileName);
            Image barcodeImage = new Image(ImageDataFactory.create(barcodeBytes));

            // 设置条形码尺寸
            barcodeImage.setWidth(barcodeWidth);
            barcodeImage.setHeight(barcodeHeight);

            // 获取第一页
            PdfPage firstPage = pdfDoc.getFirstPage();
            if (firstPage == null) {
                throw new IllegalStateException("PDF文档没有页面");
            }

            // 计算条形码位置（根据水平位置选择计算X坐标）
            float pageWidth = firstPage.getPageSize().getWidth();
            float pageHeight = firstPage.getPageSize().getHeight();
            
            float absoluteX;
            float absoluteY;
            
            switch (barcodeHorizontal.toLowerCase()) {
                case "left":
                    // 左边：距离左边缘指定距离
                    absoluteX = barcodeX;
                    absoluteY = pageHeight - barcodeY - barcodeHeight;
                    break;
                case "left-bottom":
                    // 左下：距离左边缘指定距离，距离下边缘指定距离
                    absoluteX = barcodeX;
                    absoluteY = barcodeY;
                    break;
                case "left-center":
                    // 左中：距离左边缘指定距离，垂直居中
                    absoluteX = barcodeX;
                    absoluteY = (pageHeight - barcodeHeight) / 2;
                    break;
                case "left-top":
                    // 左上：距离左边缘指定距离，距离上边缘指定距离
                    absoluteX = barcodeX;
                    absoluteY = pageHeight - barcodeY - barcodeHeight;
                    break;
                case "center":
                    // 中间：水平居中，距离上边缘指定距离
                    absoluteX = (pageWidth - barcodeWidth) / 2;
                    absoluteY = pageHeight - barcodeY - barcodeHeight;
                    break;
                case "center-bottom":
                    // 中下：水平居中，距离下边缘指定距离
                    absoluteX = (pageWidth - barcodeWidth) / 2;
                    absoluteY = barcodeY;
                    break;
                case "center-center":
                    // 正中：水平垂直都居中
                    absoluteX = (pageWidth - barcodeWidth) / 2;
                    absoluteY = (pageHeight - barcodeHeight) / 2;
                    break;
                case "center-top":
                    // 中上：水平居中，距离上边缘指定距离
                    absoluteX = (pageWidth - barcodeWidth) / 2;
                    absoluteY = pageHeight - barcodeY - barcodeHeight;
                    break;
                case "right":
                default:
                    // 右边：距离右边缘指定距离，距离上边缘指定距离
                    absoluteX = pageWidth - barcodeX - barcodeWidth;
                    absoluteY = pageHeight - barcodeY - barcodeHeight;
                    break;
                case "right-bottom":
                    // 右下：距离右边缘指定距离，距离下边缘指定距离
                    absoluteX = pageWidth - barcodeX - barcodeWidth;
                    absoluteY = barcodeY;
                    break;
                case "right-center":
                    // 右中：距离右边缘指定距离，垂直居中
                    absoluteX = pageWidth - barcodeX - barcodeWidth;
                    absoluteY = (pageHeight - barcodeHeight) / 2;
                    break;
                case "right-top":
                    // 右上：距离右边缘指定距离，距离上边缘指定距离
                    absoluteX = pageWidth - barcodeX - barcodeWidth;
                    absoluteY = pageHeight - barcodeY - barcodeHeight;
                    break;
            }
            
            log.debug("页面尺寸: {}x{}px, 条形码水平位置: {}, 绝对位置: ({}, {})", pageWidth, pageHeight, barcodeHorizontal, absoluteX, absoluteY);

            // 设置条形码位置
            barcodeImage.setFixedPosition(absoluteX, absoluteY);

            // 将条形码添加到第一页
            document.add(barcodeImage);

            // 关闭文档
            document.close();
            pdfDoc.close();

            byte[] resultBytes = outputStream.toByteArray();
            log.info("PDF处理完成: {}, 处理后大小: {} bytes", fileName, resultBytes.length);
            
            return resultBytes;

        } catch (Exception e) {
            log.error("处理PDF文件失败: {}, 错误: {}", fileName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 验证PDF文件是否有效
     *
     * @param pdfBytes PDF文件字节数组
     * @return 是否有效
     */
    public static boolean isValidPdf(byte[] pdfBytes) {
        if (pdfBytes == null || pdfBytes.length == 0) {
            return false;
        }

        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);
             PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream))) {
            int pageCount = pdfDoc.getNumberOfPages();
            
            log.debug("PDF验证成功，页数: {}", pageCount);
            return pageCount > 0;
            
        } catch (Exception e) {
            log.warn("PDF验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取PDF页数
     *
     * @param pdfBytes PDF文件字节数组
     * @return 页数
     * @throws Exception 处理异常
     */
    public static int getPageCount(byte[] pdfBytes) throws Exception {
        if (pdfBytes == null || pdfBytes.length == 0) {
            return 0;
        }

        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);
             PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream))) {
            int pageCount = pdfDoc.getNumberOfPages();
            return pageCount;
        }
    }

    /**
     * 获取PDF页面尺寸
     *
     * @param pdfBytes PDF文件字节数组
     * @return 页面尺寸数组 [宽度, 高度]
     * @throws Exception 处理异常
     */
    public static float[] getPageSize(byte[] pdfBytes) throws Exception {
        if (pdfBytes == null || pdfBytes.length == 0) {
            return new float[]{0, 0};
        }

        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfBytes);
             PdfDocument pdfDoc = new PdfDocument(new PdfReader(inputStream))) {
            PdfPage firstPage = pdfDoc.getFirstPage();
            
            if (firstPage == null) {
                return new float[]{0, 0};
            }
            
            float width = firstPage.getPageSize().getWidth();
            float height = firstPage.getPageSize().getHeight();
            
            return new float[]{width, height};
        }
    }
}
