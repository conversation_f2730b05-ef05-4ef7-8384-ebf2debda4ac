package org.dromara.tianxin.mapper;

import org.dromara.tianxin.domain.OutwardProcessLab;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
public interface OutwardProcessLabMapper extends BaseMapperPlus<OutwardProcessLab, OutwardProcessLabVo> {

}
