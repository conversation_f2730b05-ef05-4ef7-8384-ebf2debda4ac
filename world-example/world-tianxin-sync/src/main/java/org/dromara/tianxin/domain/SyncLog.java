package org.dromara.tianxin.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 天心天思数据同步日志对象 t_sync_log
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sync_log")
public class SyncLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 同步模块
     */
    @TableField("module")
    private String module;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 数据ID
     */
    @TableField("data_id")
    private String dataId;

    /**
     * 同步状态：0-失败，1-成功
     */
    @TableField("sync_status")
    private Integer syncStatus;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    private LocalDateTime syncTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 请求数据
     */
    @TableField("request_data")
    private String requestData;

    /**
     * 响应数据
     */
    @TableField("response_data")
    private String responseData;

    /**
     * 响应时间（毫秒）
     */
    @TableField("response_time")
    private Long responseTime;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;
}
