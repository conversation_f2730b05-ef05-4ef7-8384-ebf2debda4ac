package org.dromara.tianxin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.MachineToolInfo;
import org.dromara.tianxin.domain.bo.MachineToolInfoBo;
import org.dromara.tianxin.domain.vo.MachineToolInfoVo;
import org.dromara.tianxin.mapper.MachineToolInfoMapper;
import org.dromara.tianxin.service.IMachineToolInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MachineToolInfoServiceImpl implements IMachineToolInfoService {

    private final MachineToolInfoMapper baseMapper;

    /**
     * 查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param id 主键
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     */
    @Override
    public MachineToolInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等分页列表
     */
    @Override
    public TableDataInfo<MachineToolInfoVo> queryPageList(MachineToolInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MachineToolInfo> lqw = buildQueryWrapper(bo);
        Page<MachineToolInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     *
     * @param bo 查询条件
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     */
    @Override
    public List<MachineToolInfoVo> queryList(MachineToolInfoBo bo) {
        LambdaQueryWrapper<MachineToolInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<MachineToolInfoVo> queryAll() {
        return baseMapper.selectVoList();
    }

    private LambdaQueryWrapper<MachineToolInfo> buildQueryWrapper(MachineToolInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MachineToolInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MachineToolInfo::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMachineTool()), MachineToolInfo::getMachineTool, bo.getMachineTool());
        lqw.eq(StringUtils.isNotBlank(bo.getResource()), MachineToolInfo::getResource, bo.getResource());
        lqw.eq(StringUtils.isNotBlank(bo.getIsScannable()), MachineToolInfo::getIsScannable, bo.getIsScannable());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), MachineToolInfo::getCategory, bo.getCategory());
        lqw.eq(StringUtils.isNotBlank(bo.getOperator()), MachineToolInfo::getOperator, bo.getOperator());
        lqw.between(params.get("beginOperationTime") != null && params.get("endOperationTime") != null,
            MachineToolInfo::getOperationTime, params.get("beginOperationTime"), params.get("endOperationTime"));
        return lqw;
    }

    /**
     * 新增机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param bo 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MachineToolInfoBo bo) {
        MachineToolInfo add = MapstructUtils.convert(bo, MachineToolInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param bo 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MachineToolInfoBo bo) {
        MachineToolInfo update = MapstructUtils.convert(bo, MachineToolInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MachineToolInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
