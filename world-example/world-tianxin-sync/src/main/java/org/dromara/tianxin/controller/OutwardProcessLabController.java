package org.dromara.tianxin.controller;

import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.OutwardProcessLabBo;
import org.dromara.tianxin.domain.dto.MailSendDto;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.service.IMailService;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等 前端访问路由地址为:/tianxin/processLab
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/processLab")
public class OutwardProcessLabController extends BaseController {

    private final IOutwardProcessLabService outwardProcessLabService;
    private final IMailService mailService;

    /**
     * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @SaCheckPermission("tianxin:processLab:list")
    @GetMapping("/list")
    public TableDataInfo<OutwardProcessLabVo> list(OutwardProcessLabBo bo, PageQuery pageQuery) {
        return outwardProcessLabService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @SaCheckPermission("tianxin:processLab:export")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OutwardProcessLabBo bo, HttpServletResponse response) {
        List<OutwardProcessLabVo> list = outwardProcessLabService.queryList(bo);
        ExcelUtil.exportExcel(list, "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", OutwardProcessLabVo.class, response);
    }

    /**
     * 获取外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:processLab:query")
    @GetMapping("/{id}")
    public R<OutwardProcessLabVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(outwardProcessLabService.queryById(id));
    }

    /**
     * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @SaCheckPermission("tianxin:processLab:add")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OutwardProcessLabBo bo) {
        return toAjax(outwardProcessLabService.insertByBo(bo));
    }

    /**
     * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @SaCheckPermission("tianxin:processLab:edit")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OutwardProcessLabBo bo) {
        return toAjax(outwardProcessLabService.updateByBo(bo));
    }

    /**
     * 删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:processLab:remove")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(outwardProcessLabService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取外发加工信息
     *
     * @param bo 条件
     * @return 列表
     */
    @GetMapping("/getOutSourceList")
    public R<List<OutsourceVo>> getOutSourceList(OutwardProcessLabBo bo) {
        return R.ok(outwardProcessLabService.getOutSourceList(bo));
    }

    /**
     * 发送邮件
     *
     * @param mailSendDto 邮件发送请求参数
     * @return 发送结果
     */
    @SaCheckPermission("tianxin:processLab:mail")
    @Log(title = "外发加工标签信息", businessType = BusinessType.OTHER)
    @PostMapping("/sendMail")
    public R<Void> sendMail(@Validated @RequestBody MailSendDto mailSendDto) {
        try {
            String result = mailService.sendMail(mailSendDto);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送外发加工标签通知邮件
     *
     * @param to 收件人
     * @param processLabIds 外发加工标签ID列表
     * @return 发送结果
     */
    @SaCheckPermission("tianxin:processLab:mail")
    @Log(title = "外发加工标签通知", businessType = BusinessType.OTHER)
    @PostMapping("/sendProcessLabNotification")
    public R<Void> sendProcessLabNotification(String to, @RequestBody List<Long> processLabIds) {
        try {
            String result = mailService.sendProcessLabNotification(to, processLabIds);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送紧急外发加工标签通知邮件
     *
     * @param to 收件人
     * @param processLabIds 外发加工标签ID列表
     * @return 发送结果
     */
    @SaCheckPermission("tianxin:processLab:mail")
    @Log(title = "紧急外发加工标签通知", businessType = BusinessType.OTHER)
    @PostMapping("/sendUrgentProcessLabNotification")
    public R<Void> sendUrgentProcessLabNotification(String to, @RequestBody List<Long> processLabIds) {
        try {
            String result = mailService.sendUrgentProcessLabNotification(to, processLabIds);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("邮件发送失败：" + e.getMessage());
        }
    }

}
