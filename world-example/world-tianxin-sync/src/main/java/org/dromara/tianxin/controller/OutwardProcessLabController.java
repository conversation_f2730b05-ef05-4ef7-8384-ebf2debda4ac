package org.dromara.tianxin.controller;

import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.OutwardProcessLabBo;
import org.dromara.tianxin.domain.dto.MailSendDto;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等 前端访问路由地址为:/tianxin/processLab
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/processLab")
public class OutwardProcessLabController extends BaseController {

    private final IOutwardProcessLabService outwardProcessLabService;

    /**
     * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @SaCheckPermission("tianxin:processLab:list")
    @GetMapping("/list")
    public TableDataInfo<OutwardProcessLabVo> list(OutwardProcessLabBo bo, PageQuery pageQuery) {
        return outwardProcessLabService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @SaCheckPermission("tianxin:processLab:export")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OutwardProcessLabBo bo, HttpServletResponse response) {
        List<OutwardProcessLabVo> list = outwardProcessLabService.queryList(bo);
        ExcelUtil.exportExcel(list, "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", OutwardProcessLabVo.class, response);
    }

    /**
     * 获取外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:processLab:query")
    @GetMapping("/{id}")
    public R<OutwardProcessLabVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(outwardProcessLabService.queryById(id));
    }

    /**
     * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @SaCheckPermission("tianxin:processLab:add")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OutwardProcessLabBo bo) {
        return toAjax(outwardProcessLabService.insertByBo(bo));
    }

    /**
     * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @SaCheckPermission("tianxin:processLab:edit")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OutwardProcessLabBo bo) {
        return toAjax(outwardProcessLabService.updateByBo(bo));
    }

    /**
     * 删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:processLab:remove")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(outwardProcessLabService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取外发加工信息
     *
     * @param bo 条件
     * @return 列表
     */
    @GetMapping("/getOutSourceList")
    public R<List<OutsourceVo>> getOutSourceList(OutwardProcessLabBo bo) {
        return R.ok(outwardProcessLabService.getOutSourceList(bo));
    }

    /**
     * 发送邮件
     *
     * @param mailSendDto 邮件发送请求参数
     * @return 发送结果
     */
    @SaCheckPermission("tianxin:processLab:mail")
    @Log(title = "外发加工标签邮件发送", businessType = BusinessType.OTHER)
    @PostMapping("/sendMail")
    public R<Void> sendMail(@Validated @RequestBody MailSendDto mailSendDto) {
        try {
            // 构建邮件内容
            String emailContent = buildEmailContent(mailSendDto);

            // 发送邮件
            if (mailSendDto.getIsHtml()) {
                MailUtils.sendHtml(mailSendDto.getTo(), mailSendDto.getSubject(), emailContent);
            } else {
                MailUtils.sendText(mailSendDto.getTo(), mailSendDto.getSubject(), emailContent);
            }

            return R.ok("邮件发送成功");
        } catch (Exception e) {
            return R.fail("邮件发送失败：" + e.getMessage());
        }
    }

    /**
     * 构建邮件内容
     *
     * @param mailSendDto 邮件发送参数
     * @return 邮件内容
     */
    private String buildEmailContent(MailSendDto mailSendDto) {
        StringBuilder content = new StringBuilder();

        // 添加基础内容
        content.append(mailSendDto.getContent());

        // 如果包含外发加工标签ID，则添加相关数据
        if (mailSendDto.getProcessLabIds() != null && !mailSendDto.getProcessLabIds().isEmpty()) {
            if (mailSendDto.getIsHtml()) {
                content.append("<br><br><h3>外发加工标签信息</h3>");
            } else {
                content.append("\n\n=== 外发加工标签信息 ===\n");
            }

            for (Long id : mailSendDto.getProcessLabIds()) {
                try {
                    OutwardProcessLabVo processLab = outwardProcessLabService.queryById(id);
                    if (processLab != null) {
                        if (mailSendDto.getIsHtml()) {
                            content.append(buildHtmlProcessLabInfo(processLab));
                        } else {
                            content.append(buildTextProcessLabInfo(processLab));
                        }
                    }
                } catch (Exception e) {
                    // 忽略单个标签获取失败的情况
                }
            }
        }

        return content.toString();
    }

    /**
     * 构建HTML格式的外发加工标签信息
     *
     * @param processLab 外发加工标签信息
     * @return HTML格式的信息
     */
    private String buildHtmlProcessLabInfo(OutwardProcessLabVo processLab) {
        return String.format(
            "<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px;'>" +
            "<h4>外发加工标签 - ID: %d</h4>" +
            "<p><strong>PR号:</strong> %s</p>" +
            "<p><strong>品号:</strong> %s</p>" +
            "<p><strong>描述:</strong> %s</p>" +
            "<p><strong>数量:</strong> %s %s</p>" +
            "<p><strong>厂商:</strong> %s (%s)</p>" +
            "<p><strong>状态:</strong> %s</p>" +
            "</div>",
            processLab.getId(),
            processLab.getPrNo() != null ? processLab.getPrNo() : "",
            processLab.getPrdNo() != null ? processLab.getPrdNo() : "",
            processLab.getPrdDesc() != null ? processLab.getPrdDesc() : "",
            processLab.getQty() != null ? processLab.getQty() : "",
            processLab.getUt() != null ? processLab.getUt() : "",
            processLab.getVendorName() != null ? processLab.getVendorName() : "",
            processLab.getVendorCode() != null ? processLab.getVendorCode() : "",
            processLab.getSta() != null ? processLab.getSta() : ""
        );
    }

    /**
     * 构建文本格式的外发加工标签信息
     *
     * @param processLab 外发加工标签信息
     * @return 文本格式的信息
     */
    private String buildTextProcessLabInfo(OutwardProcessLabVo processLab) {
        return String.format(
            "\n--- 外发加工标签 - ID: %d ---\n" +
            "PR号: %s\n" +
            "品号: %s\n" +
            "描述: %s\n" +
            "数量: %s %s\n" +
            "厂商: %s (%s)\n" +
            "状态: %s\n" +
            "--------------------------------\n",
            processLab.getId(),
            processLab.getPrNo() != null ? processLab.getPrNo() : "",
            processLab.getPrdNo() != null ? processLab.getPrdNo() : "",
            processLab.getPrdDesc() != null ? processLab.getPrdDesc() : "",
            processLab.getQty() != null ? processLab.getQty() : "",
            processLab.getUt() != null ? processLab.getUt() : "",
            processLab.getVendorName() != null ? processLab.getVendorName() : "",
            processLab.getVendorCode() != null ? processLab.getVendorCode() : "",
            processLab.getSta() != null ? processLab.getSta() : ""
        );
    }

}
