package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 外发加工信息视图对象 outsource
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Data
@ExcelIgnoreUnannotated
public class OutsourceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNo;

    /**
     * 图号
     */
    @ExcelProperty(value = "图号")
    private String dwgNo;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String customerCode;

    /**
     * PO类型
     */
    @ExcelProperty(value = "PO类型")
    private String poType;

    /**
     * 供应商简称
     */
    @ExcelProperty(value = "供应商简称")
    private String vendorSnm;

    /**
     * 订单数量
     */
    @ExcelProperty(value = "订单数量")
    private Long orderQuantity;

    /**
     * 工序内容
     */
    @ExcelProperty(value = "工序内容")
    private String processContent;

    /**
     * 零件分类
     */
    @ExcelProperty(value = "零件分类")
    private String partClassification;

}