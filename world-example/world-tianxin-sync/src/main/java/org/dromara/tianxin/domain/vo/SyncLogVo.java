package org.dromara.tianxin.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.tianxin.domain.SyncLog;

import java.time.LocalDateTime;

/**
 * 天心天思数据同步日志视图对象
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SyncLog.class)
public class SyncLogVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 同步模块
     */
    private String module;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 数据ID
     */
    private String dataId;

    /**
     * 同步状态：0-失败，1-成功
     */
    private Integer syncStatus;

    /**
     * 同步状态名称
     */
    private String syncStatusName;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
