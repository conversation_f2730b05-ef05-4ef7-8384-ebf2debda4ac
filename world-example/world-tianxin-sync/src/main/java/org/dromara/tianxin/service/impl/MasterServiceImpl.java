package org.dromara.tianxin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.tianxin.domain.Master;
import org.dromara.tianxin.domain.ScanRecordLog;
import org.dromara.tianxin.domain.bo.MasterBo;
import org.dromara.tianxin.domain.vo.MasterVo;
import org.dromara.tianxin.mapper.MasterMapper;
import org.dromara.tianxin.mapper.ScanRecordLogMapper;
import org.dromara.tianxin.service.IMasterService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * PMC订单变更管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-11
 */

@Service
@RequiredArgsConstructor
public class MasterServiceImpl implements IMasterService {

    private final MasterMapper baseMapper;
    private final ScanRecordLogMapper scanRecordLogMapper;

    /**
     * 查询PMC订单变更管理
     *
     * @param id 主键
     * @return PMC订单变更管理
     */
    @Override
    public MasterVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询PMC订单变更管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return PMC订单变更管理分页列表
     */
    @Override
    public TableDataInfo<MasterVo> queryPageList(MasterBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Master> lqw = buildQueryWrapper(bo);
        Page<MasterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的PMC订单变更管理列表
     *
     * @param bo 查询条件
     * @return PMC订单变更管理列表
     */
    @Override
    public List<MasterVo> queryList(MasterBo bo) {
        LambdaQueryWrapper<Master> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Master> buildQueryWrapper(MasterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Master> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Master::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getSta()), Master::getSta, bo.getSta());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerCode()), Master::getCustomerCode, bo.getCustomerCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerPoNo()), Master::getCustomerPoNo, bo.getCustomerPoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), Master::getMoNo, bo.getMoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDwgNo()), Master::getDwgNo, bo.getDwgNo());
        // 完工状态：
        // 未完成(未结案)： Master中的字段 sta1=0 or sta1=9
        // 完成(结案)： Master中的字段 sta1<>0 and sta1<>9
        switch (bo.getFinishStatus()) {
            case "未完成":
                lqw.and(lqw1 -> lqw1.eq(Master::getSta1, 0).or().eq(Master::getSta1, 9));
                break;
            case "已完成":
                lqw.and(lqw1 -> lqw1.ne(Master::getSta1, 0).ne(Master::getSta1, 9));
                break;
        }

        return lqw;
    }

    /**
     * 新增PMC订单变更管理
     *
     * @param bo PMC订单变更管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MasterBo bo) {
        Master add = MapstructUtils.convert(bo, Master.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改PMC订单变更管理
     *
     * @param bo PMC订单变更管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MasterBo bo) {
        Master update = MapstructUtils.convert(bo, Master.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean updateBatchByBo(List<MasterBo> boList) {
        List<Master> updateList = MapstructUtils.convert(boList, Master.class);
        if (updateList == null || updateList.isEmpty()) {
            return true;
        }
        for (Master update : updateList) {
            validEntityBeforeSave(update);
        }
        return baseMapper.updateBatchById(updateList);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Master entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除PMC订单变更管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void handleProcessMethod(MasterBo bo, String processMethod) {
        LambdaQueryWrapper<Master> lqw = buildQueryWrapper(bo);
        List<Master> masters = baseMapper.selectList(lqw);
        if (masters.isEmpty()) {
            return;
        }
        // 支持批量订单操作；
        // Update master set sta=@处理方式, action =@处理方式 , update_time =getdate() where mo_no=@MO号
        LambdaUpdateWrapper<Master> luw = new LambdaUpdateWrapper<>();
        luw.set(Master::getSta, processMethod);
        luw.set(Master::getAction, processMethod);
        luw.in(Master::getMoNo, masters.stream().map(Master::getMoNo).toList());
        baseMapper.update(luw);

        // 特殊情况处理要求：
        // 当处理方式=暂停时 , master 表中的字段 sta1=4
        // 当处理方式=关闭时 , master 表中的字段 sta1=8
        // 当处理方式=待更新图纸时 , master 表中的字段 sta1=9
        // 当处理方式=已更新图纸时 , master 表中的字段 sta1=0
        // 当处理方式=恢复时 , master 表中的字段 sta1=0
        switch (processMethod) {
            case "暂停" -> {
                luw = new LambdaUpdateWrapper<>();
                luw.set(Master::getSta1, 4);
                luw.in(Master::getMoNo, masters.stream().map(Master::getMoNo).toList());
                baseMapper.update(luw);
            }
            case "关闭" -> {
                luw = new LambdaUpdateWrapper<>();
                luw.set(Master::getSta1, 8);
                luw.in(Master::getMoNo, masters.stream().map(Master::getMoNo).toList());
                baseMapper.update(luw);
            }
            case "待更新图纸" -> {
                luw = new LambdaUpdateWrapper<>();
                luw.set(Master::getSta1, 9);
                luw.in(Master::getMoNo, masters.stream().map(Master::getMoNo).toList());
                baseMapper.update(luw);
            }
            case "已更新图纸", "恢复" -> {
                luw = new LambdaUpdateWrapper<>();
                luw.set(Master::getSta1, 0);
                luw.in(Master::getMoNo, masters.stream().map(Master::getMoNo).toList());
                baseMapper.update(luw);
            }
        }

        // Insert into scan_record_log (mo_no, operator, operator_name, start_time, end_time, action)
        // @MO号,操作人工号，操作人姓名，开始时间，结束时间，@处理方式
        List<ScanRecordLog> scanRecordLogs = masters.stream().map(master -> {
            ScanRecordLog scanRecordLog = new ScanRecordLog();
            scanRecordLog.setMoNo(master.getMoNo());
            scanRecordLog.setOperator(LoginHelper.getUsername());
            scanRecordLog.setOperatorName(LoginHelper.getUsername());
            scanRecordLog.setStartTime(new Date());
            scanRecordLog.setEndTime(new Date());
            scanRecordLog.setAction(processMethod);
            return scanRecordLog;
        }).toList();
        if (!scanRecordLogs.isEmpty()) {
            scanRecordLogMapper.insertBatch(scanRecordLogs);
        }
    }
}
