package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.tianxin.domain.ScanRecordLog;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息视图对象 scan_record_log
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ScanRecordLog.class)
public class ScanRecordLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNo;

    /**
     * 工序号
     */
    @ExcelProperty(value = "工序号")
    private Long processNo;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 操作员
     */
    @ExcelProperty(value = "操作员")
    private String operator;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String operatorName;

    /**
     * 机台号
     */
    @ExcelProperty(value = "机台号")
    private String machineNo;

    /**
     * 动作
     */
    @ExcelProperty(value = "动作")
    private String action;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 通过数量
     */
    @ExcelProperty(value = "通过数量")
    private Long passedQuantity;

    /**
     * 报废数量
     */
    @ExcelProperty(value = "报废数量")
    private Long scrappedQuantity;

    /**
     * 总进度
     */
    @ExcelProperty(value = "总进度")
    private Long totalProgress;

    /**
     * 当前进度
     */
    @ExcelProperty(value = "当前进度")
    private Long currentProgress;

    /**
     * 工艺工时
     */
    @ExcelProperty(value = "工艺工时")
    private Long processManHours;

    /**
     * 实际时间
     */
    @ExcelProperty(value = "实际时间")
    private Long actualTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String customerCode;

    /**
     * 供应商
     */
    @ExcelProperty(value = "供应商")
    private String supplier;

    /**
     * 储位
     */
    @ExcelProperty(value = "储位")
    private String storageLocation;

    /**
     * 品号
     */
    @ExcelProperty(value = "品号")
    private String partNo;

    /**
     * 图号
     */
    @ExcelProperty(value = "图号")
    private String drawingNo;

    /**
     * 物料描述
     */
    @ExcelProperty(value = "物料描述")
    private String materialDescription;

    /**
     * 客户产品料号
     */
    @ExcelProperty(value = "客户产品料号")
    private String customerProductNo;

    /**
     * 客户产品名称
     */
    @ExcelProperty(value = "客户产品名称")
    private String customerProductName;

    /**
     * 批号
     */
    @ExcelProperty(value = "批号")
    private String batchNo;

    /**
     * 电脑名
     */
    @ExcelProperty(value = "电脑名")
    private String computerName;

    /**
     * 行业
     */
    @ExcelProperty(value = "行业")
    private String industry;

    /**
     * 排产工时
     */
    @ExcelProperty(value = "排产工时")
    private Long productionScheduleManHours;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date creationTime;


}
