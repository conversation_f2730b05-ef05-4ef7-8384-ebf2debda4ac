package org.dromara.tianxin.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等对象 machine_tool_info
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("machine_tool_info")
public class MachineToolInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 机床
     */
    private String machineTool;

    /**
     * 资源
     */
    private String resource;

    /**
     * 序号
     */
    private Long serialNo;

    /**
     * 可扫描
     */
    private String isScannable;

    /**
     * 分组
     */
    private String grouping;

    /**
     * 类别
     */
    private String category;

    /**
     * 使用状态
     */
    private String usageStatus;

    /**
     * 停用日期
     */
    private Date deactivationDate;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Long delFlag;


}
