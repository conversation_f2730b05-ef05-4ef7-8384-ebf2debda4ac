package org.dromara.tianxin.service.impl;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tianxin.client.TianxinApiClient;
import org.dromara.common.tianxin.domain.request.CustomerRequest;
import org.dromara.common.tianxin.domain.request.ProductRequest;
import org.dromara.common.tianxin.domain.request.SupplierRequest;
import org.dromara.common.tianxin.domain.response.ApiResult;
import org.dromara.common.tianxin.enums.SyncStatus;
import org.dromara.tianxin.domain.bo.SyncLogBo;
import org.dromara.tianxin.domain.vo.SyncLogVo;
import org.dromara.tianxin.mapper.SyncLogMapper;
import org.dromara.tianxin.service.ISyncLogService;
import org.dromara.tianxin.service.ISyncService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 天心天思数据同步服务实现
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SyncServiceImpl implements ISyncService {

    private final TianxinApiClient apiClient;
    private final ISyncLogService syncLogService;
    private final SyncLogMapper syncLogMapper;

    // ==================== 基础资料同步 ====================

    /**
     * 同步商品数据
     *
     * @param requests 商品请求对象列表
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncProducts(List<ProductRequest> requests) {
        log.info("开始同步商品数据，数量: {}", requests.size());

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (ProductRequest request : requests) {
            try {
                ApiResult<Object> apiResult = apiClient.syncProduct(request);

                if (apiResult.isSuccess()) {
                    successCount++;
                    syncLogService.recordSyncLog(
                        "PRODUCT", "PRODUCT", request.getProductCode(),
                        SyncStatus.SUCCESS.getCode(), null,
                        JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult.getData()),
                        apiResult.getResponseTime()
                    );
                    log.info("商品同步成功: {}", request.getProductCode());
                } else {
                    failCount++;
                    String errorMsg = apiResult.getErrorMessage();
                    errors.add(request.getProductCode() + ": " + errorMsg);
                    syncLogService.recordSyncLog(
                        "PRODUCT", "PRODUCT", request.getProductCode(),
                        SyncStatus.FAILED.getCode(), errorMsg,
                        JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult),
                        apiResult.getResponseTime()
                    );
                    log.error("商品同步失败: {} - {}", request.getProductCode(), errorMsg);
                }
            } catch (Exception e) {
                failCount++;
                String errorMsg = "同步异常: " + e.getMessage();
                errors.add(request.getProductCode() + ": " + errorMsg);
                syncLogService.recordSyncLog(
                    "PRODUCT", "PRODUCT", request.getProductCode(),
                    SyncStatus.FAILED.getCode(), errorMsg,
                    JSONUtil.toJsonStr(request), null, null
                );
                log.error("商品同步异常: {}", request.getProductCode(), e);
            }
        }

        result.put("totalCount", requests.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("message", String.format("商品数据同步完成，成功: %d，失败: %d", successCount, failCount));

        log.info("商品数据同步完成，成功: {}，失败: {}", successCount, failCount);
        return result;
    }

    /**
     * 同步单个商品数据
     *
     * @param request 商品请求对象
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncProduct(ProductRequest request) {
        log.info("开始同步单个商品数据: {}", request.getProductCode());

        try {
            ApiResult<Object> apiResult = apiClient.syncProduct(request);

            if (apiResult.isSuccess()) {
                syncLogService.recordSyncLog(
                    "PRODUCT", "PRODUCT", request.getProductCode(),
                    SyncStatus.SUCCESS.getCode(), null,
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult.getData()),
                    apiResult.getResponseTime()
                );
                log.info("商品同步成功: {}", request.getProductCode());
                return Map.of("success", true, "message", "商品同步成功", "data", apiResult.getData());
            } else {
                String errorMsg = apiResult.getErrorMessage();
                syncLogService.recordSyncLog(
                    "PRODUCT", "PRODUCT", request.getProductCode(),
                    SyncStatus.FAILED.getCode(), errorMsg,
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult),
                    apiResult.getResponseTime()
                );
                log.error("商品同步失败: {} - {}", request.getProductCode(), errorMsg);
                return Map.of("success", false, "message", "商品同步失败: " + errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = "同步异常: " + e.getMessage();
            syncLogService.recordSyncLog(
                "PRODUCT", "PRODUCT", request.getProductCode(),
                SyncStatus.FAILED.getCode(), errorMsg,
                JSONUtil.toJsonStr(request), null, null
            );
            log.error("商品同步异常: {}", request.getProductCode(), e);
            return Map.of("success", false, "message", errorMsg);
        }
    }

    /**
     * 同步客户数据
     *
     * @param requests 客户请求对象列表
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncCustomers(List<CustomerRequest> requests) {
        log.info("开始同步客户数据，数量: {}", requests.size());

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (CustomerRequest request : requests) {
            try {
                ApiResult<Object> apiResult = apiClient.syncCustomer(request);

                if (apiResult.isSuccess()) {
                    successCount++;
                    syncLogService.recordSyncLog(
                        "CUSTOMER", "CUSTOMER", request.getCustomerCode(),
                        SyncStatus.SUCCESS.getCode(), null,
                        JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult.getData()),
                        apiResult.getResponseTime()
                    );
                    log.info("客户同步成功: {}", request.getCustomerCode());
                } else {
                    failCount++;
                    String errorMsg = apiResult.getErrorMessage();
                    errors.add(request.getCustomerCode() + ": " + errorMsg);
                    syncLogService.recordSyncLog(
                        "CUSTOMER", "CUSTOMER", request.getCustomerCode(),
                        SyncStatus.FAILED.getCode(), errorMsg,
                        JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult),
                        apiResult.getResponseTime()
                    );
                    log.error("客户同步失败: {} - {}", request.getCustomerCode(), errorMsg);
                }
            } catch (Exception e) {
                failCount++;
                String errorMsg = "同步异常: " + e.getMessage();
                errors.add(request.getCustomerCode() + ": " + errorMsg);
                syncLogService.recordSyncLog(
                    "CUSTOMER", "CUSTOMER", request.getCustomerCode(),
                    SyncStatus.FAILED.getCode(), errorMsg,
                    JSONUtil.toJsonStr(request), null, null
                );
                log.error("客户同步异常: {}", request.getCustomerCode(), e);
            }
        }

        result.put("totalCount", requests.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("message", String.format("客户数据同步完成，成功: %d，失败: %d", successCount, failCount));

        log.info("客户数据同步完成，成功: {}，失败: {}", successCount, failCount);
        return result;
    }

    /**
     * 同步单个客户数据
     *
     * @param request 客户请求对象
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncCustomer(CustomerRequest request) {
        log.info("开始同步单个客户数据: {}", request.getCustomerCode());

        try {
            ApiResult<Object> apiResult = apiClient.syncCustomer(request);

            if (apiResult.isSuccess()) {
                syncLogService.recordSyncLog(
                    "CUSTOMER", "CUSTOMER", request.getCustomerCode(),
                    SyncStatus.SUCCESS.getCode(), null,
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult.getData()),
                    apiResult.getResponseTime()
                );
                log.info("客户同步成功: {}", request.getCustomerCode());
                return Map.of("success", true, "message", "客户同步成功", "data", apiResult.getData());
            } else {
                String errorMsg = apiResult.getErrorMessage();
                syncLogService.recordSyncLog(
                    "CUSTOMER", "CUSTOMER", request.getCustomerCode(),
                    SyncStatus.FAILED.getCode(), errorMsg,
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult),
                    apiResult.getResponseTime()
                );
                log.error("客户同步失败: {} - {}", request.getCustomerCode(), errorMsg);
                return Map.of("success", false, "message", "客户同步失败: " + errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = "同步异常: " + e.getMessage();
            syncLogService.recordSyncLog(
                "CUSTOMER", "CUSTOMER", request.getCustomerCode(),
                SyncStatus.FAILED.getCode(), errorMsg,
                JSONUtil.toJsonStr(request), null, null
            );
            log.error("客户同步异常: {}", request.getCustomerCode(), e);
            return Map.of("success", false, "message", errorMsg);
        }
    }

    /**
     * 同步供应商数据
     *
     * @param requests 供应商请求对象列表
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncSuppliers(List<SupplierRequest> requests) {
        log.info("开始同步供应商数据，数量: {}", requests.size());

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        for (SupplierRequest request : requests) {
            try {
                ApiResult<Object> apiResult = apiClient.syncSupplier(request);

                if (apiResult.isSuccess()) {
                    successCount++;
                    syncLogService.recordSyncLog(
                        "SUPPLIER", "SUPPLIER", request.getSupplierCode(),
                        SyncStatus.SUCCESS.getCode(), null,
                        JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult.getData()),
                        apiResult.getResponseTime()
                    );
                    log.info("供应商同步成功: {}", request.getSupplierCode());
                } else {
                    failCount++;
                    String errorMsg = apiResult.getErrorMessage();
                    errors.add(request.getSupplierCode() + ": " + errorMsg);
                    syncLogService.recordSyncLog(
                        "SUPPLIER", "SUPPLIER", request.getSupplierCode(),
                        SyncStatus.FAILED.getCode(), errorMsg,
                        JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult),
                        apiResult.getResponseTime()
                    );
                    log.error("供应商同步失败: {} - {}", request.getSupplierCode(), errorMsg);
                }
            } catch (Exception e) {
                failCount++;
                String errorMsg = "同步异常: " + e.getMessage();
                errors.add(request.getSupplierCode() + ": " + errorMsg);
                syncLogService.recordSyncLog(
                    "SUPPLIER", "SUPPLIER", request.getSupplierCode(),
                    SyncStatus.FAILED.getCode(), errorMsg,
                    JSONUtil.toJsonStr(request), null, null
                );
                log.error("供应商同步异常: {}", request.getSupplierCode(), e);
            }
        }

        result.put("totalCount", requests.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        result.put("message", String.format("供应商数据同步完成，成功: %d，失败: %d", successCount, failCount));

        log.info("供应商数据同步完成，成功: {}，失败: {}", successCount, failCount);
        return result;
    }

    /**
     * 同步单个供应商数据
     *
     * @param request 供应商请求对象
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncSupplier(SupplierRequest request) {
        log.info("开始同步单个供应商数据: {}", request.getSupplierCode());

        try {
            ApiResult<Object> apiResult = apiClient.syncSupplier(request);

            if (apiResult.isSuccess()) {
                syncLogService.recordSyncLog(
                    "SUPPLIER", "SUPPLIER", request.getSupplierCode(),
                    SyncStatus.SUCCESS.getCode(), null,
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult.getData()),
                    apiResult.getResponseTime()
                );
                log.info("供应商同步成功: {}", request.getSupplierCode());
                return Map.of("success", true, "message", "供应商同步成功", "data", apiResult.getData());
            } else {
                String errorMsg = apiResult.getErrorMessage();
                syncLogService.recordSyncLog(
                    "SUPPLIER", "SUPPLIER", request.getSupplierCode(),
                    SyncStatus.FAILED.getCode(), errorMsg,
                    JSONUtil.toJsonStr(request), JSONUtil.toJsonStr(apiResult),
                    apiResult.getResponseTime()
                );
                log.error("供应商同步失败: {} - {}", request.getSupplierCode(), errorMsg);
                return Map.of("success", false, "message", "供应商同步失败: " + errorMsg);
            }
        } catch (Exception e) {
            String errorMsg = "同步异常: " + e.getMessage();
            syncLogService.recordSyncLog(
                "SUPPLIER", "SUPPLIER", request.getSupplierCode(),
                SyncStatus.FAILED.getCode(), errorMsg,
                JSONUtil.toJsonStr(request), null, null
            );
            log.error("供应商同步异常: {}", request.getSupplierCode(), e);
            return Map.of("success", false, "message", errorMsg);
        }
    }

    // ==================== 业务数据同步 ====================

    /**
     * 同步库存数据
     *
     * @return 同步结果
     */
    @Override
    public Object syncInventory() {
        log.info("开始同步库存数据");
        // TODO: 实现库存数据同步逻辑
        return Map.of("success", true, "message", "库存数据同步功能待实现");
    }

    /**
     * 同步销售订单数据
     *
     * @return 同步结果
     */
    @Override
    public Object syncSalesOrders() {
        log.info("开始同步销售订单数据");
        // TODO: 实现销售订单数据同步逻辑
        return Map.of("success", true, "message", "销售订单数据同步功能待实现");
    }

    /**
     * 同步采购订单数据
     *
     * @return 同步结果
     */
    @Override
    public Object syncPurchaseOrders() {
        log.info("开始同步采购订单数据");
        // TODO: 实现采购订单数据同步逻辑
        return Map.of("success", true, "message", "采购订单数据同步功能待实现");
    }

    /**
     * 同步生产计划数据
     *
     * @return 同步结果
     */
    @Override
    public Object syncProductionPlans() {
        log.info("开始同步生产计划数据");
        // TODO: 实现生产计划数据同步逻辑
        return Map.of("success", true, "message", "生产计划数据同步功能待实现");
    }

    /**
     * 同步生产订单数据
     *
     * @return 同步结果
     */
    @Override
    public Object syncProductionOrders() {
        log.info("开始同步生产订单数据");
        // TODO: 实现生产订单数据同步逻辑
        return Map.of("success", true, "message", "生产订单数据同步功能待实现");
    }

    // ==================== 通用同步方法 ====================

    /**
     * 执行全量数据同步
     *
     * @return 同步结果
     */
    @Override
    public Object executeFullSync() {
        log.info("开始执行全量数据同步");
        // TODO: 实现全量数据同步逻辑
        return Map.of("success", true, "message", "全量数据同步功能待实现");
    }

    /**
     * 执行增量数据同步
     *
     * @return 同步结果
     */
    @Override
    public Object executeIncrementalSync() {
        log.info("开始执行增量数据同步");
        // TODO: 实现增量数据同步逻辑
        return Map.of("success", true, "message", "增量数据同步功能待实现");
    }

    /**
     * 检查同步状态
     *
     * @return 同步状态
     */
    @Override
    public Object checkSyncStatus() {
        log.info("检查同步状态");

        Map<String, Object> status = new HashMap<>();

        // 检查API连接状态
        try {
            ApiResult<Object> healthResult = apiClient.healthCheck();
            status.put("apiConnected", healthResult.isSuccess());
            status.put("apiMessage", healthResult.getErrorMessage());
        } catch (Exception e) {
            status.put("apiConnected", false);
            status.put("apiMessage", "API连接异常: " + e.getMessage());
        }

        // 获取同步统计信息
        status.put("productStats", syncLogService.getSyncStatistics("PRODUCT", "PRODUCT"));
        status.put("customerStats", syncLogService.getSyncStatistics("CUSTOMER", "CUSTOMER"));
        status.put("supplierStats", syncLogService.getSyncStatistics("SUPPLIER", "SUPPLIER"));

        return status;
    }

    /**
     * 重试失败的同步任务
     *
     * @return 重试结果
     */
    @Override
    public Object retryFailedSync() {
        log.info("开始重试失败的同步任务");
        // TODO: 实现重试失败的同步任务逻辑
        return Map.of("success", true, "message", "重试失败同步任务功能待实现");
    }

    // ==================== 分页查询方法 ====================

    /**
     * 分页查询同步日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<SyncLogVo> selectPageSyncLogList(SyncLogBo bo, PageQuery pageQuery) {
        log.info("分页查询同步日志列表，查询条件: {}", bo);

        // 调用Mapper进行分页查询
        var page = syncLogMapper.selectPageSyncLogList(
            pageQuery.build(),
            bo.getModule(),
            bo.getDataType(),
            bo.getSyncStatus(),
            bo.getDataId()
        );

        // 处理查询结果，设置状态名称
        page.getRecords().forEach(syncLogVo -> {
            // 设置同步状态名称
            if (syncLogVo.getSyncStatus() != null) {
                syncLogVo.setSyncStatusName(
                    syncLogVo.getSyncStatus() == 1 ? "成功" : "失败"
                );
            }
            // 设置同步时间（使用创建时间）
            syncLogVo.setSyncTime(syncLogVo.getCreateTime());
        });

        log.info("分页查询同步日志列表完成，总记录数: {}", page.getTotal());
        return TableDataInfo.build(page);
    }
}
