package org.dromara.tianxin.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.tianxin.service.IPdfBarcodeService;
import org.dromara.tianxin.utils.PdfBarcodeProcessor;
import org.dromara.common.core.utils.file.ZipCompressor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * PDF条形码服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfBarcodeServiceImpl implements IPdfBarcodeService {

    /**
     * 线程池用于并行处理PDF文件
     */
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 最大文件大小（50MB）
     */
    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024;

    /**
     * 最大文件数量
     */
    private static final int MAX_FILE_COUNT = 50;

    /**
     * 支持的文件类型
     */
    private static final String SUPPORTED_FILE_TYPE = "application/pdf";

    @Override
    public Map<String, Object> processPdfFiles(MultipartFile[] files, String barcodeHorizontal, int barcodeX, int barcodeY, int barcodeWidth, int barcodeHeight) throws Exception {
        log.info("开始批量处理PDF文件，文件数量: {}, 条形码位置: 水平={}, 边距={}, 上边距={}, 尺寸: {}x{}",
                files.length, barcodeHorizontal, barcodeX, barcodeY, barcodeWidth, barcodeHeight);

        // 验证文件
        Map<String, Object> validationResult = validateFiles(files);
        if (!(Boolean) validationResult.get("valid")) {
            return validationResult;
        }

        // 生成任务ID
        String taskId = generateTaskId();
        log.info("生成任务ID: {}", taskId);

        try {
            // 并行处理PDF文件
            Map<String, byte[]> processedFiles = new HashMap<>();
            List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

            for (MultipartFile file : files) {
                CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return processSinglePdfFile(file, barcodeHorizontal, barcodeX, barcodeY, barcodeWidth, barcodeHeight);
                    } catch (Exception e) {
                        log.error("处理PDF文件失败: {}, 错误: {}", file.getOriginalFilename(), e.getMessage(), e);
                        Map<String, Object> errorResult = new HashMap<>();
                        errorResult.put("fileName", file.getOriginalFilename());
                        errorResult.put("success", false);
                        errorResult.put("error", e.getMessage());
                        return errorResult;
                    }
                }, executorService);

                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(5, TimeUnit.MINUTES);

            // 收集处理结果
            int successCount = 0;
            int failCount = 0;
            for (CompletableFuture<Map<String, Object>> future : futures) {
                Map<String, Object> result = future.get();
                if ((Boolean) result.get("success")) {
                    String fileName = (String) result.get("fileName");
                    byte[] processedContent = (byte[]) result.get("processedContent");
                    processedFiles.put(fileName, processedContent);
                    successCount++;
                } else {
                    failCount++;
                }
            }

            log.info("PDF文件处理完成，成功: {}, 失败: {}", successCount, failCount);

            // 创建ZIP压缩包
            String processResult = generateProcessResult(files, successCount, failCount);
            byte[] zipBytes = ZipCompressor.createZipPackage(processedFiles, processResult);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("taskId", taskId);
            result.put("zipBytes", zipBytes);
            result.put("zipFileName", "PDF条形码处理结果_" + taskId + ".zip");
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("totalCount", files.length);
            result.put("zipSize", zipBytes.length);

            log.info("任务完成: {}, ZIP包大小: {} bytes", taskId, zipBytes.length);
            return result;

        } catch (Exception e) {
            log.error("批量处理PDF文件失败: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("error", "批量处理失败: " + e.getMessage());
            errorResult.put("taskId", taskId);
            return errorResult;
        }
    }

    @Override
    public Map<String, Object> validateFiles(MultipartFile[] files) {
        Map<String, Object> result = new HashMap<>();

        if (files == null || files.length == 0) {
            result.put("valid", false);
            result.put("error", "请选择要上传的PDF文件");
            return result;
        }

        if (files.length > MAX_FILE_COUNT) {
            result.put("valid", false);
            result.put("error", "一次最多只能上传" + MAX_FILE_COUNT + "个文件");
            return result;
        }

        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                result.put("valid", false);
                result.put("error", "文件不能为空: " + file.getOriginalFilename());
                return result;
            }

            if (file.getSize() > MAX_FILE_SIZE) {
                result.put("valid", false);
                result.put("error", "文件过大: " + file.getOriginalFilename() + "，最大支持" + (MAX_FILE_SIZE / 1024 / 1024) + "MB");
                return result;
            }

            String contentType = file.getContentType();
            if (contentType == null || !contentType.equals(SUPPORTED_FILE_TYPE)) {
                result.put("valid", false);
                result.put("error", "不支持的文件类型: " + file.getOriginalFilename() + "，只支持PDF文件");
                return result;
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".pdf")) {
                result.put("valid", false);
                result.put("error", "文件扩展名错误: " + originalFilename + "，只支持.pdf文件");
                return result;
            }
        }

        result.put("valid", true);
        result.put("message", "文件验证通过");
        return result;
    }

    @Override
    public Map<String, Object> getProcessStatus(String taskId) {
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", taskId);
        result.put("status", 1); // 1-成功，2-失败，0-处理中
        result.put("message", "任务已完成");
        result.put("totalCount", 0);
        result.put("successCount", 0);
        result.put("failCount", 0);
        result.put("successRate", 100.0);
        result.put("startTime", LocalDateTime.now());
        result.put("completeTime", LocalDateTime.now());
        result.put("duration", 0L);
        return result;
    }

    @Override
    public boolean cleanupTempFiles(String taskId) {
        // 简化实现，由于我们使用内存处理，不需要清理临时文件
        log.info("清理任务临时文件: {}", taskId);
        return true;
    }

    /**
     * 处理单个PDF文件
     *
     * @param file PDF文件
     * @param barcodeHorizontal 条形码水平位置（left/center/right）
     * @param barcodeX 条形码距离边缘的距离（像素）
     * @param barcodeY 条形码距离上边缘的距离（像素）
     * @param barcodeWidth 条形码宽度（像素）
     * @param barcodeHeight 条形码高度（像素）
     * @return 处理结果
     * @throws Exception 处理异常
     */
    private Map<String, Object> processSinglePdfFile(MultipartFile file, String barcodeHorizontal, int barcodeX, int barcodeY, int barcodeWidth, int barcodeHeight) throws Exception {
        String fileName = file.getOriginalFilename();
        log.info("开始处理PDF文件: {}", fileName);

        // 读取文件内容
        byte[] fileBytes = file.getBytes();

        // 验证PDF文件
        if (!PdfBarcodeProcessor.isValidPdf(fileBytes)) {
            throw new IllegalArgumentException("无效的PDF文件: " + fileName);
        }

        // 处理PDF文件，添加条形码
        byte[] processedBytes = PdfBarcodeProcessor.addBarcodeToPdf(fileBytes, fileName, barcodeHorizontal, barcodeX, barcodeY, barcodeWidth, barcodeHeight);

        Map<String, Object> result = new HashMap<>();
        result.put("fileName", fileName);
        result.put("success", true);
        result.put("processedContent", processedBytes);
        result.put("originalSize", fileBytes.length);
        result.put("processedSize", processedBytes.length);

        log.info("PDF文件处理成功: {}, 原始大小: {} bytes, 处理后大小: {} bytes", fileName, fileBytes.length, processedBytes.length);

        return result;
    }

    /**
     * 生成处理结果说明
     *
     * @param files        原始文件数组
     * @param successCount 成功数量
     * @param failCount    失败数量
     * @return 处理结果说明
     */
    private String generateProcessResult(MultipartFile[] files, int successCount, int failCount) {
        StringBuilder result = new StringBuilder();
        result.append("PDF条形码处理结果\n");
        result.append("==================\n\n");
        result.append("处理时间: ").append(DateUtil.date()).append("\n");
        result.append("上传文件总数: ").append(files.length).append("\n");
        result.append("成功处理: ").append(successCount).append(" 个文件\n");
        result.append("处理失败: ").append(failCount).append(" 个文件\n");
        result.append("成功率: ").append(String.format("%.1f", (double) successCount / files.length * 100)).append("%\n\n");

        if (successCount > 0) {
            result.append("处理成功的文件:\n");
            for (MultipartFile file : files) {
                if (file != null && file.getOriginalFilename() != null) {
                    result.append("✓ ").append(file.getOriginalFilename()).append("\n");
                }
            }
        }

        if (failCount > 0) {
            result.append("\n处理失败的文件:\n");
            result.append("请检查文件是否为有效的PDF格式\n");
        }

        return result.toString();
    }

    /**
     * 生成任务ID
     *
     * @return 任务ID
     */
    private String generateTaskId() {
        return "TASK_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
