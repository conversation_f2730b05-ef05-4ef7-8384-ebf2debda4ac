package org.dromara.tianxin.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.tianxin.domain.ScanRecordLog;
import org.dromara.tianxin.domain.bo.ScanRecordLogBo;
import org.dromara.tianxin.domain.vo.ScanRecordLogVo;
import org.dromara.tianxin.mapper.ScanRecordLogMapper;
import org.dromara.tianxin.service.IScanRecordLogService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@RequiredArgsConstructor
@Service
public class ScanRecordLogServiceImpl implements IScanRecordLogService {

    private final ScanRecordLogMapper baseMapper;

    /**
     * 查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param id 主键
     * @return 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     */
    @Override
    public ScanRecordLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息分页列表
     */
    @Override
    public TableDataInfo<ScanRecordLogVo> queryPageList(ScanRecordLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ScanRecordLog> lqw = buildQueryWrapper(bo);
        Page<ScanRecordLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     *
     * @param bo 查询条件
     * @return 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     */
    @Override
    public List<ScanRecordLogVo> queryList(ScanRecordLogBo bo) {
        LambdaQueryWrapper<ScanRecordLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ScanRecordLog> buildQueryWrapper(ScanRecordLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ScanRecordLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ScanRecordLog::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), ScanRecordLog::getMoNo, bo.getMoNo());
        lqw.eq(bo.getProcessNo() != null, ScanRecordLog::getProcessNo, bo.getProcessNo());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), ScanRecordLog::getProcessName, bo.getProcessName());
        lqw.eq(StringUtils.isNotBlank(bo.getOperator()), ScanRecordLog::getOperator, bo.getOperator());
        lqw.like(StringUtils.isNotBlank(bo.getOperatorName()), ScanRecordLog::getOperatorName, bo.getOperatorName());
        lqw.eq(StringUtils.isNotBlank(bo.getMachineNo()), ScanRecordLog::getMachineNo, bo.getMachineNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAction()), ScanRecordLog::getAction, bo.getAction());
        return lqw;
    }

    /**
     * 新增扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param bo 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ScanRecordLogBo bo) {
        ScanRecordLog add = MapstructUtils.convert(bo, ScanRecordLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param bo 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ScanRecordLogBo bo) {
        ScanRecordLog update = MapstructUtils.convert(bo, ScanRecordLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ScanRecordLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
