package org.dromara.tianxin.domain.vo;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 组织动作信息VO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
public class OrganizationActionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    private Long deptId;

    /**
     * 组织名称
     */
    private String deptName;

    /**
     * 动作信息列表
     */
    private List<ActionInfoVo> actionList;

    /**
     * 动作总数
     */
    private Integer actionCount;

    public OrganizationActionVo() {
    }

    public OrganizationActionVo(Long deptId, String deptName, List<ActionInfoVo> actionList) {
        this.deptId = deptId;
        this.deptName = deptName;
        this.actionList = actionList;
        this.actionCount = actionList != null ? actionList.size() : 0;
    }
}
