package org.dromara.tianxin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.SyncLog;
import org.dromara.tianxin.domain.bo.SyncLogBo;
import org.dromara.tianxin.domain.vo.SyncLogVo;
import org.dromara.tianxin.mapper.SyncLogMapper;
import org.dromara.tianxin.service.ISyncLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 天心天思数据同步日志Service业务层处理
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SyncLogServiceImpl implements ISyncLogService {

    private final SyncLogMapper baseMapper;

    /**
     * 查询天心天思数据同步日志
     *
     * @param id 主键
     * @return 同步日志
     */
    @Override
    public SyncLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询天心天思数据同步日志列表
     *
     * @param syncLog 查询条件
     * @return 同步日志列表
     */
    @Override
    public TableDataInfo<SyncLogVo> queryPageList(SyncLogBo syncLog, PageQuery pageQuery) {
        LambdaQueryWrapper<SyncLog> lqw = buildQueryWrapper(syncLog);
        Page<SyncLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询天心天思数据同步日志列表
     *
     * @param syncLog 查询条件
     * @return 同步日志列表
     */
    @Override
    public List<SyncLogVo> queryList(SyncLogBo syncLog) {
        LambdaQueryWrapper<SyncLog> lqw = buildQueryWrapper(syncLog);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SyncLog> buildQueryWrapper(SyncLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SyncLog> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getModule()), SyncLog::getModule, bo.getModule());
        lqw.like(StringUtils.isNotBlank(bo.getDataType()), SyncLog::getDataType, bo.getDataType());
        lqw.like(StringUtils.isNotBlank(bo.getDataId()), SyncLog::getDataId, bo.getDataId());
        lqw.eq(bo.getSyncStatus() != null, SyncLog::getSyncStatus, bo.getSyncStatus());
        lqw.between(params.get("beginSyncTime") != null && params.get("endSyncTime") != null,
            SyncLog::getSyncTime, params.get("beginSyncTime"), params.get("endSyncTime"));
        lqw.orderByDesc(SyncLog::getSyncTime);
        return lqw;
    }

    /**
     * 新增天心天思数据同步日志
     *
     * @param bo 同步日志业务对象
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SyncLogBo bo) {
        SyncLog add = MapstructUtils.convert(bo, SyncLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改天心天思数据同步日志
     *
     * @param bo 同步日志业务对象
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(SyncLogBo bo) {
        SyncLog update = MapstructUtils.convert(bo, SyncLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SyncLog entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除天心天思数据同步日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 记录同步日志
     *
     * @param module       模块
     * @param dataType     数据类型
     * @param dataId       数据ID
     * @param syncStatus   同步状态
     * @param errorMessage 错误信息
     * @param requestData  请求数据
     * @param responseData 响应数据
     * @param responseTime 响应时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordSyncLog(String module, String dataType, String dataId, Integer syncStatus,
                              String errorMessage, String requestData, String responseData, Long responseTime) {
        try {
            SyncLog syncLog = new SyncLog();
            syncLog.setModule(module);
            syncLog.setDataType(dataType);
            syncLog.setDataId(dataId);
            syncLog.setSyncStatus(syncStatus);
            syncLog.setSyncTime(LocalDateTime.now());
            syncLog.setErrorMessage(errorMessage);
            syncLog.setRequestData(requestData);
            syncLog.setResponseData(responseData);
            syncLog.setResponseTime(responseTime);
            syncLog.setRetryCount(0);

            baseMapper.insert(syncLog);

            log.info("记录同步日志成功: {} - {} - {} - {}", module, dataType, dataId, syncStatus);
        } catch (Exception e) {
            log.error("记录同步日志失败: {} - {} - {}", module, dataType, dataId, e);
        }
    }

    /**
     * 查询同步统计信息
     *
     * @param module   模块
     * @param dataType 数据类型
     * @return 统计信息
     */
    @Override
    public Object getSyncStatistics(String module, String dataType) {
        LambdaQueryWrapper<SyncLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(module), SyncLog::getModule, module);
        lqw.eq(StringUtils.isNotBlank(dataType), SyncLog::getDataType, dataType);

        // 总记录数
        long totalCount = baseMapper.selectCount(lqw);

        // 成功记录数
        lqw.eq(SyncLog::getSyncStatus, 1);
        long successCount = baseMapper.selectCount(lqw);

        // 失败记录数
        long failCount = totalCount - successCount;

        // 成功率
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;

        return Map.of(
            "totalCount", totalCount,
            "successCount", successCount,
            "failCount", failCount,
            "successRate", String.format("%.2f%%", successRate)
        );
    }
}
