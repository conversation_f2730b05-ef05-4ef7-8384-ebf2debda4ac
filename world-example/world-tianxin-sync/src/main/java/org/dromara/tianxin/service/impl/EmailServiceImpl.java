package org.dromara.tianxin.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.tianxin.service.IEmailService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 邮件服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements IEmailService {

    @Value("${tianxin.email.default-recipients:<EMAIL>}")
    private String defaultRecipients;

    @Value("${tianxin.email.temp-dir:${java.io.tmpdir}}")
    private String tempDir;

    @Override
    public void sendProcessLabEmail(MultipartFile[] files, String content, String tableData, String subject) {
        try {
            // 解析表格数据
            List<Map<String, Object>> dataList = JSONUtil.toList(tableData, Map.class);
            
            // 构建邮件内容
            StringBuilder emailContent = new StringBuilder();
            emailContent.append("<h2>外发加工标签数据</h2>");
            
            if (StrUtil.isNotBlank(content)) {
                emailContent.append("<p>").append(content).append("</p>");
            }
            
            // 添加数据统计信息
            emailContent.append("<p>本次发送数据共 <strong>").append(dataList.size()).append("</strong> 条记录。</p>");
            
            // 构建HTML表格
            if (!dataList.isEmpty()) {
                emailContent.append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>");
                
                // 表头
                emailContent.append("<thead style='background-color: #f2f2f2;'><tr>");
                Map<String, Object> firstRow = dataList.get(0);
                for (String key : firstRow.keySet()) {
                    emailContent.append("<th>").append(getColumnDisplayName(key)).append("</th>");
                }
                emailContent.append("</tr></thead>");
                
                // 表体（只显示前10条数据，避免邮件过大）
                emailContent.append("<tbody>");
                int maxRows = Math.min(dataList.size(), 10);
                for (int i = 0; i < maxRows; i++) {
                    Map<String, Object> row = dataList.get(i);
                    emailContent.append("<tr>");
                    for (String key : firstRow.keySet()) {
                        Object value = row.get(key);
                        emailContent.append("<td>").append(value != null ? value.toString() : "").append("</td>");
                    }
                    emailContent.append("</tr>");
                }
                
                if (dataList.size() > 10) {
                    emailContent.append("<tr><td colspan='").append(firstRow.size())
                               .append("' style='text-align: center; font-style: italic;'>")
                               .append("... 还有 ").append(dataList.size() - 10).append(" 条数据，详见附件")
                               .append("</td></tr>");
                }
                
                emailContent.append("</tbody></table>");
            }
            
            // 发送邮件
            String emailSubject = StrUtil.isNotBlank(subject) ? subject : "外发加工标签数据";
            sendEmailWithAttachments(defaultRecipients, emailSubject, emailContent.toString(), files);
            
            log.info("外发加工标签邮件发送成功，数据条数：{}", dataList.size());
            
        } catch (Exception e) {
            log.error("发送外发加工标签邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public void sendSimpleEmail(String to, String subject, String content) {
        try {
            MailUtils.sendText(to, subject, content);
            log.info("简单邮件发送成功，收件人：{}", to);
        } catch (Exception e) {
            log.error("发送简单邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String content) {
        try {
            MailUtils.sendHtml(to, subject, content);
            log.info("HTML邮件发送成功，收件人：{}", to);
        } catch (Exception e) {
            log.error("发送HTML邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public void sendEmailWithAttachments(String to, String subject, String content, MultipartFile[] files) {
        try {
            List<File> attachmentFiles = new ArrayList<>();
            
            // 处理附件
            if (files != null && files.length > 0) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        // 创建临时文件
                        Path tempFile = Files.createTempFile(
                            Path.of(tempDir), 
                            "email_attachment_", 
                            "_" + file.getOriginalFilename()
                        );
                        
                        // 复制文件内容到临时文件
                        Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
                        attachmentFiles.add(tempFile.toFile());
                    }
                }
            }
            
            // 发送邮件
            if (attachmentFiles.isEmpty()) {
                MailUtils.sendHtml(to, subject, content);
            } else {
                MailUtils.sendHtml(to, subject, content, attachmentFiles.toArray(new File[0]));
            }
            
            // 清理临时文件
            for (File tempFile : attachmentFiles) {
                try {
                    Files.deleteIfExists(tempFile.toPath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败：{}", tempFile.getAbsolutePath(), e);
                }
            }
            
            log.info("带附件邮件发送成功，收件人：{}，附件数量：{}", to, files != null ? files.length : 0);
        } catch (Exception e) {
            log.error("发送带附件邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    /**
     * 获取列显示名称
     */
    private String getColumnDisplayName(String key) {
        switch (key) {
            case "id": return "ID";
            case "prtSw": return "PRT_SW";
            case "prNo": return "PR号";
            case "prItm": return "PR行";
            case "prType": return "申请类型";
            case "prdNo": return "品号";
            case "prdDesc": return "描述";
            case "qty": return "数量";
            case "ut": return "单位";
            case "prDate": return "请求日期";
            case "pmcRequestDate": return "PMC要求日期";
            case "vendorCode": return "厂商代号";
            case "vendorSnm": return "厂商简称";
            case "vendorName": return "厂商名";
            case "unitPrice": return "单价";
            case "currency": return "货币";
            case "isProvideMaterials": return "是否供料";
            case "dwgNo": return "图号";
            case "pjNo": return "PJ号";
            case "moNo": return "MO号";
            case "purchaseNum": return "订单号";
            case "batchNo": return "批号";
            case "sumPrice": return "总价";
            case "sta": return "状态";
            case "outsourcingDate": return "外发交期";
            case "custNo": return "客户代码";
            case "zcNo": return "工序号";
            case "machiningCenter": return "加工中心";
            case "zcName": return "工序名称";
            case "epfSnm": return "电镀厂商";
            case "electroplateContent": return "电镀内容";
            case "specifiedMaterials": return "指定材料";
            case "isElectroplate": return "是否电镀";
            case "epfCode": return "电镀厂商代号";
            case "epfName": return "电镀厂商名";
            case "usr": return "用户";
            case "sysDate": return "系统日期";
            case "host": return "主机";
            case "rem": return "备注";
            default: return key;
        }
    }
}
