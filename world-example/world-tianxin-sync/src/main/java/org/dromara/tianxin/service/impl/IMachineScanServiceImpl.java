package org.dromara.tianxin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.ScanRecordLog;
import org.dromara.tianxin.domain.bo.MachineScanBo;
import org.dromara.tianxin.domain.vo.ScanRecordLogVo;
import org.dromara.tianxin.mapper.ScanRecordLogMapper;
import org.dromara.tianxin.service.IMachineScanService;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IMachineScanServiceImpl implements IMachineScanService {
    private final ScanRecordLogMapper scanRecordLogMapper;

    @Override
    public TableDataInfo<ScanRecordLogVo> queryPageList(MachineScanBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ScanRecordLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ScanRecordLog::getStartTime);
        // 动作筛选
        lqw.eq(StringUtils.isNotBlank(bo.getAction()), ScanRecordLog::getAction, bo.getAction());
        // 查询未结束的记录
        lqw.isNull(ScanRecordLog::getEndTime);
        // 机台筛选
        lqw.in(ObjectUtil.isNotEmpty(bo.getMachines()), ScanRecordLog::getMachineNo, bo.getMachines());
        Page<ScanRecordLogVo> page = scanRecordLogMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }
}
