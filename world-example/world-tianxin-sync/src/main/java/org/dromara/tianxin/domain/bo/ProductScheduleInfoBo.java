package org.dromara.tianxin.domain.bo;

import org.dromara.tianxin.domain.ProductScheduleInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息业务对象 product_schedule_info
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductScheduleInfo.class, reverseConvertGenerate = false)
public class ProductScheduleInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 排产单号
     */
    private String apsNo;

    /**
     * 排产项次
     */
    private Long itm;

    /**
     * MO号
     */
    private String moNo;

    /**
     * 工序号
     */
    private Long zcItm;

    /**
     * 工序名称
     */
    private String zcNm;

    /**
     * 制程代号
     */
    private String zcNo;

    /**
     * 总工时
     */
    private Long zgs;

    /**
     * 机台
     */
    private String dep;

    /**
     * ERP总工时
     */
    private Long zgsErp;

    /**
     * 调试时间
     */
    private Long waittime;

    /**
     * 开工时间
     */
    private Date kgDd;

    /**
     * 排产开始时间
     */
    private Date bDd;

    /**
     * 排产结束时间
     */
    private Date eDd;

    /**
     * 机台数
     */
    private Long jts;

    /**
     * 托工厂商
     */
    private String cusNo;


}
