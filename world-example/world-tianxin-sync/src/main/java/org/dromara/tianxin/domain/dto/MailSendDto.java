package org.dromara.tianxin.domain.dto;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 邮件发送请求DTO
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
public class MailSendDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收件人邮箱地址（多个用逗号分隔）
     */
    @NotBlank(message = "收件人不能为空")
    private String to;

    /**
     * 抄送人邮箱地址（多个用逗号分隔）
     */
    private String cc;

    /**
     * 密送人邮箱地址（多个用逗号分隔）
     */
    private String bcc;

    /**
     * 邮件主题
     */
    @NotBlank(message = "邮件主题不能为空")
    private String subject;

    /**
     * 邮件内容
     */
    @NotBlank(message = "邮件内容不能为空")
    private String content;

    /**
     * 是否为HTML格式
     */
    private Boolean isHtml = false;

    /**
     * 外发加工标签ID列表（用于发送相关数据）
     */
    private List<Long> processLabIds;

    /**
     * 附件文件路径列表
     */
    private List<String> attachmentPaths;
}
