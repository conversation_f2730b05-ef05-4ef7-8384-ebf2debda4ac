package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.tianxin.domain.ProductScheduleInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息视图对象 product_schedule_info
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductScheduleInfo.class)
public class ProductScheduleInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 排产单号
     */
    @ExcelProperty(value = "排产单号")
    private String apsNo;

    /**
     * 排产项次
     */
    @ExcelProperty(value = "排产项次")
    private Long itm;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNo;

    /**
     * 工序号
     */
    @ExcelProperty(value = "工序号")
    private Long zcItm;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String zcNm;

    /**
     * 制程代号
     */
    @ExcelProperty(value = "制程代号")
    private String zcNo;

    /**
     * 总工时
     */
    @ExcelProperty(value = "总工时")
    private Long zgs;

    /**
     * 机台
     */
    @ExcelProperty(value = "机台")
    private String dep;

    /**
     * ERP总工时
     */
    @ExcelProperty(value = "ERP总工时")
    private Long zgsErp;

    /**
     * 调试时间
     */
    @ExcelProperty(value = "调试时间")
    private Long waittime;

    /**
     * 开工时间
     */
    @ExcelProperty(value = "开工时间")
    private Date kgDd;

    /**
     * 排产开始时间
     */
    @ExcelProperty(value = "排产开始时间")
    private Date bDd;

    /**
     * 排产结束时间
     */
    @ExcelProperty(value = "排产结束时间")
    private Date eDd;

    /**
     * 机台数
     */
    @ExcelProperty(value = "机台数")
    private Long jts;

    /**
     * 托工厂商
     */
    @ExcelProperty(value = "托工厂商")
    private String cusNo;


}
