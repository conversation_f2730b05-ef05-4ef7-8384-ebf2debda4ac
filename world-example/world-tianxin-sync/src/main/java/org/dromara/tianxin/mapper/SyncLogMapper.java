package org.dromara.tianxin.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.tianxin.domain.SyncLog;
import org.dromara.tianxin.domain.vo.SyncLogVo;

/**
 * 天心天思数据同步日志Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SyncLogMapper extends BaseMapperPlus<SyncLog, SyncLogVo> {

    /**
     * 分页查询同步日志列表
     *
     * @param page 分页对象
     * @param module 同步模块
     * @param dataType 数据类型
     * @param syncStatus 同步状态
     * @param dataId 数据ID
     * @return 分页结果
     */
    IPage<SyncLogVo> selectPageSyncLogList(Page<SyncLogVo> page,
                                          @Param("module") String module,
                                          @Param("dataType") String dataType,
                                          @Param("syncStatus") Integer syncStatus,
                                          @Param("dataId") String dataId);
}
