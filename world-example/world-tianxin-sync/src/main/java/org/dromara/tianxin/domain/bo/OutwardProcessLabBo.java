package org.dromara.tianxin.domain.bo;

import org.dromara.tianxin.domain.OutwardProcessLab;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等业务对象 outward_process_lab
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OutwardProcessLab.class, reverseConvertGenerate = false)
public class OutwardProcessLabBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * PRT_SW
     */
    private String prtSw;

    /**
     * PR号
     */
    private String prNo;

    /**
     * PR行
     */
    private String prItm;

    /**
     * 申请类型
     */
    private String prType;

    /**
     * 品号
     */
    private String prdNo;

    /**
     * 描述
     */
    private String prdDesc;

    /**
     * 数量
     */
    private Long qty;

    /**
     * 单位
     */
    private String ut;

    /**
     * 请求日期
     */
    private Date prDate;

    /**
     * PMC要求日期
     */
    private String pmcRequestDate;

    /**
     * 厂商代号
     */
    private String vendorCode;

    /**
     * 简称
     */
    private String vendorSnm;

    /**
     * 厂商名
     */
    private String vendorName;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 货币
     */
    private String currency;

    /**
     * 是否供料
     */
    private String isProvideMaterials;

    /**
     * 图号
     */
    private String dwgNo;

    /**
     * PJ号
     */
    private String pjNo;

    /**
     * MO号
     */
    private String moNo;

    /**
     * 订单号
     */
    private String purchaseNum;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 总价
     */
    private Long sumPrice;

    /**
     * 状态
     */
    private String sta;

    /**
     * 外发交期
     */
    private Date outsourcingDate;

    /**
     * 客户代码
     */
    private String custNo;

    /**
     * 工序号
     */
    private String zcNo;

    /**
     * 加工中心
     */
    private String machiningCenter;

    /**
     * 工序名称
     */
    private String zcName;

    /**
     * 电镀厂商
     */
    private String epfSnm;

    /**
     * 电镀内容
     */
    private String electroplateContent;

    /**
     * 指定材料
     */
    private String specifiedMaterials;

    /**
     * 是否电镀
     */
    private String isElectroplate;

    /**
     * 电镀厂商代号
     */
    private String epfCode;

    /**
     * 电镀厂商名
     */
    private String epfName;

    /**
     * USR
     */
    private String usr;

    /**
     * SYS_DATE
     */
    private Date sysDate;

    /**
     * host
     */
    private String host;

    /**
     * 备注
     */
    private String rem;


}
