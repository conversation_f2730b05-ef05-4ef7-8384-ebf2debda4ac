package org.dromara.mes.domain.bo;

import org.dromara.mes.domain.ProProcess;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 工序管理业务对象 pro_process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProProcess.class, reverseConvertGenerate = false)
public class ProProcessBo extends BaseEntity {

    /**
     * 工序ID
     */
    @NotNull(message = "工序ID不能为空", groups = { EditGroup.class })
    private Long processId;

    /**
     * 工序编码
     */
    @NotBlank(message = "工序编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String processCode;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String processName;

    /**
     * 工作中心
     */
    private String workCenter;

    /**
     * 是否启用
     */
    @NotBlank(message = "是否启用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String enableFlag;

    /**
     * 是否生成描述
     */
    @NotBlank(message = "是否生成描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String descFlag;

    /**
     * 是否外托运
     */
    @NotBlank(message = "是否外托运不能为空", groups = { AddGroup.class, EditGroup.class })
    private String invoiceFlag;

    /**
     * 是否电镀
     */
    @NotBlank(message = "是否电镀不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platingFlag;

    /**
     * 总机时是否为0
     */
    @NotBlank(message = "总机时是否为0不能为空", groups = { AddGroup.class, EditGroup.class })
    private String noYesHours;

    /**
     * 电镀类型
     */
    private String platingType;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 急单等待时间
     */
    private Long urgWaitTime;

    /**
     * 急单等待时间
     */
    private Long waitTime;

    /**
     * 批量等待时间
     */
    private Long batchWaitTime;

    /**
     * 备注
     */
    private String remark;


}
