package org.dromara.mes.domain.bo;

import org.dromara.mes.domain.ProRoute;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 工艺路线业务对象 pro_route
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProRoute.class, reverseConvertGenerate = false)
public class ProRouteBo extends BaseEntity {

    /**
     * 工艺路线ID
     */
    @NotNull(message = "工艺路线ID不能为空", groups = { EditGroup.class })
    private Long routeId;

    /**
     * 工艺路线编号
     */
    @NotBlank(message = "工艺路线编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String routeCode;

    /**
     * 工艺路线名称
     */
    @NotBlank(message = "工艺路线名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String routeName;

    /**
     * 工艺路线说明
     */
    private String routeDesc;

    /**
     * 是否启用
     */
    @NotBlank(message = "是否启用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String enableFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
