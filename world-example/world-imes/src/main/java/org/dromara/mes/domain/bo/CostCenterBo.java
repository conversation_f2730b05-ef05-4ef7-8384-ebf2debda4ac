package org.dromara.mes.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.mes.domain.CostCenter;

/**
 * 售后成本中心业务对象 cost_center
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CostCenter.class, reverseConvertGenerate = false)
public class CostCenterBo extends BaseEntity {

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 成本中心代码
     */
    @NotBlank(message = "成本中心代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String costCenterCode;

    /**
     * 成本中心名称
     */
    @NotBlank(message = "成本中心名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String costCenterName;

    /**
     * 单价
     */
    private Double costCenterPrice;


}
