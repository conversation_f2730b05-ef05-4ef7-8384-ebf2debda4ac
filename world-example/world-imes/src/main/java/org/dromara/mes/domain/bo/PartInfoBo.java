package org.dromara.mes.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.mes.domain.PartInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 物料档案信息业务对象 part_info
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@AutoMapper(target = PartInfo.class, reverseConvertGenerate = false)
public class PartInfoBo implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 长度(mm)
     */
    private Long length1;

    /**
     * 物料类型
     */
    private String partType;

    /**
     * 规格描述
     */
    private String spec;

    /**
     * 高度(mm)
     */
    private Long high1;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 物料组
     */
    private String partGroup;

    /**
     * 宽度(mm)
     */
    private Long width1;

    /**
     * 物料编码
     */
    private String partId;

    /**
     * 物料名称
     */
    private String partName;

    /**
     * 批次管理标识
     */
    private String batchManag;

    /**
     * 生产组
     */
    private String prodGroup;

    /**
     * 工厂代码
     */
    private String plantCode;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 图号
     */
    private String drawNum;

    /**
     * 供应商代码
     */
    private String compCode;

    /**
     * 是否禁用
     */
    private String unable;

    /**
     * 国产/进口
     */
    private String domesticOrImport;

    /**
     * 正品/替代品
     */
    private String authenticAlterna;

    /**
     * 密度(g/cm³)
     */
    private Long density;

    /**
     * 品牌备注
     */
    private String brandRemarks;

    /**
     * 图纸版本说明
     */
    private String drawNumVersion;

    /**
     * 客户代码
     */
    private String custCode;

    /**
     * 行业标准(Y/N)
     */
    private String industryStd;

    /**
     * 状态(0禁用/1启用)
     */
    private String status;

    /**
     * 米重(kg/m)
     */
    private Long meterWeight;

    /**
     * 图纸等级
     */
    private String drawingLevel;

    /**
     * 标准工艺
     */
    private String stdProcess;

    /**
     * 直径(mm)
     */
    private Long diameter;

    /**
     * 内径(mm)
     */
    private Long insideDiameter;

    /**
     * 外径(mm)
     */
    private Long outerDiameter;

    /**
     * 包装尺寸
     */
    private String boxSize;

    /**
     * SAP同步状态
     */
    private String sapSyncInfo;

    /**
     * 产线编号
     */
    private String lineNum;

    /**
     * 需求订单号
     */
    private String reqOrderNum;

    /**
     * SAP更新状态
     */
    private String sapUpdateInfo;

    /**
     * 原始物料号
     */
    private String originalItem;

    /**
     * 多标准管控
     */
    private String manyStandard;

    /**
     * 研发估值
     */
    private Long rdValuation;
    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();

}
