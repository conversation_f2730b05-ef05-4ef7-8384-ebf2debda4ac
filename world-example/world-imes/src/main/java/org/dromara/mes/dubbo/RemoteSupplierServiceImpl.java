package org.dromara.mes.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.mes.service.ISupplierInfoService;
import org.dromara.system.api.RemoteSupplierService;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 供应商服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteSupplierServiceImpl implements RemoteSupplierService {

    private final ISupplierInfoService supplierInfoService;

    /**
     * 通过工厂租户ID信息获取该工厂供应商
     *
     * @param tenantId 租户id
     * @return 结果
     */
    @Override
    public Boolean syncSapToSupplierAsync(String tenantId) {
        Boolean result = false;
        if (StringUtils.isBlank(tenantId)) {
            throw new ServiceException("操作失败,缺少工厂TenantId参数");
        }
        // 调用异步方法
        CompletableFuture<Boolean> future = supplierInfoService.syncSapToSupplierAsync(tenantId);
        try {
            result = future.get(1, TimeUnit.MINUTES);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            R.fail("等待同步结果时发生错误", e);
        }
        return result;
    }
}
