package org.dromara.mes.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.mes.domain.BgAction;

/**
 * 报工动作业务对象 bg_action
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BgAction.class, reverseConvertGenerate = false)
public class BgActionBo extends BaseEntity {

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 序号
     */
    private Long sortNum;

    /**
     * 动作代码
     */
    private String actionCode;

    /**
     * 动作名称
     */
    private String actionName;

    /**
     * 部门代码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 在用
     */
    private String isUse;

    /**
     * 需同步SAP
     */
    private String isSap;

    /**
     * 需同步Master
     */
    private String isMaster;

    /**
     * 需要机床
     */
    private String needMachin;

    /**
     * 需单独关单
     */
    private String needClose;

    /**
     * 外发
     */
    private String isOut;

    /**
     * 多次报工
     */
    private String isMult;


}
