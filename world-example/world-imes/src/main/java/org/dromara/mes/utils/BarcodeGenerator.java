package org.dromara.mes.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 条形码生成工具类
 * 支持Code128A格式，字母自动转大写
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class BarcodeGenerator {

    /**
     * 默认条形码宽度
     */
    private static final int DEFAULT_WIDTH = 300;
    
    /**
     * 默认条形码高度
     */
    private static final int DEFAULT_HEIGHT = 100;
    
    /**
     * 默认条形码类型
     */
    private static final BarcodeFormat DEFAULT_FORMAT = BarcodeFormat.CODE_128;

    /**
     * 生成Code128A条形码
     *
     * @param content 条形码内容
     * @return 条形码图片字节数组
     * @throws WriterException 写入异常
     * @throws IOException IO异常
     */
    public static byte[] generateCode128A(String content) throws WriterException, IOException {
        return generateCode128A(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成Code128A条形码
     *
     * @param content 条形码内容
     * @param width 宽度
     * @param height 高度
     * @return 条形码图片字节数组
     * @throws WriterException 写入异常
     * @throws IOException IO异常
     */
    public static byte[] generateCode128A(String content, int width, int height) throws WriterException, IOException {
        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("条形码内容不能为空");
        }

        // 清理内容：去除特殊字符，只保留字母数字
        String cleanContent = cleanContent(content);
        
        // 字母转大写
        cleanContent = cleanContent.toUpperCase();
        
        log.info("生成条形码，原始内容: {}, 清理后内容: {}, 尺寸: {}x{}", content, cleanContent, width, height);

        // 设置编码提示
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 0);

        // 创建Code128编码器
        Code128Writer writer = new Code128Writer();
        BitMatrix bitMatrix = writer.encode(cleanContent, DEFAULT_FORMAT, width, height, hints);

        // 转换为BufferedImage
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = image.createGraphics();
        
        // 设置背景色为白色
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, height);
        
        // 设置条形码颜色为黑色
        graphics.setColor(Color.BLACK);
        
        // 绘制条形码
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                if (bitMatrix.get(x, y)) {
                    graphics.fillRect(x, y, 1, 1);
                }
            }
        }
        
        graphics.dispose();

        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", outputStream);
        byte[] imageBytes = outputStream.toByteArray();
        outputStream.close();

        log.info("条形码生成成功，大小: {} bytes", imageBytes.length);
        return imageBytes;
    }

    /**
     * 清理条形码内容
     * 去除特殊字符，只保留字母数字
     *
     * @param content 原始内容
     * @return 清理后的内容
     */
    private static String cleanContent(String content) {
        if (content == null) {
            return "";
        }
        
        // 去除.pdf后缀
        String cleaned = content.replaceAll("\\.pdf$", "");
        
        // 只保留字母数字
        cleaned = cleaned.replaceAll("[^a-zA-Z0-9]", "");
        
        // 如果内容过长，截取前20个字符
        if (cleaned.length() > 20) {
            cleaned = cleaned.substring(0, 20);
        }
        
        return cleaned;
    }

    /**
     * 验证条形码内容是否有效
     *
     * @param content 内容
     * @return 是否有效
     */
    public static boolean isValidContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        String cleaned = cleanContent(content);
        return !cleaned.isEmpty() && cleaned.length() <= 20;
    }
}
