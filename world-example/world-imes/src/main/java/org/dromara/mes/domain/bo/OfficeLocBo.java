package org.dromara.mes.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.mes.domain.OfficeLoc;

/**
 * 工作地点组业务对象 office_loc
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OfficeLoc.class, reverseConvertGenerate = false)
public class OfficeLocBo extends BaseEntity {

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 办事处代码
     */
    @NotBlank(message = "办事处代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String officeCode;

    /**
     * 描述
     */
    private String officeDesc;

    /**
     * 工号
     */
    private String workNum;

    /**
     * 主管
     */
    private String officeSupervisor;

    /**
     * 工作中心
     */
    private String workCenter;

    /**
     * 办事处员工数
     */
    private Integer totalUser;


}
