package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.PdfConvertRecordBo;
import org.dromara.mes.domain.vo.PdfConvertRecordVo;
import org.dromara.mes.service.IPdfConvertRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * PDF转换记录
 * 前端访问路由地址为:/mes/convertRecord
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/convertRecord")
public class PdfConvertRecordController extends BaseController {

    private final IPdfConvertRecordService pdfConvertRecordService;

    // 线程池用于并行处理 - 改为static final避免@RequiredArgsConstructor冲突
    private static final ExecutorService executorService = Executors.newFixedThreadPool(10);
    // API 基础地址（开发/生产）
    private static final String BASE_URL_DEV = "https://tplm.world-machining.com/world-web/api/view/BrowseFile";
    private static final String BASE_URL_PRO = "https://plm.world-machining.com/world-web/api/view/BrowseFile";

    // 认证用户名（可通过环境变量 PLM_USERNAME 覆盖，默认 WDPLM）
    private static final String DEFAULT_USERNAME = "WDPLM";

    // 从环境变量读取密码，避免在代码中明文存放
    // 开发环境密码：PLM_PASSWORD_DEV，生产环境密码：PLM_PASSWORD_PRO
    private static final String ENV_USERNAME = "WDPLM";
    private static final String ENV_PASSWORD_DEV = "12345678";
    private static final String ENV_PASSWORD_PRO = "adm123456";
    private static final String ENV_RUN_ENV = "prod"; // 可为 dev / prod


    // 使用通用存储桶常量
    private static final String BUCKET_NAME = Constants.BUCKET_BARCODE;


    /**
     * 查询PDF转换记录列表
     */
    @SaCheckPermission("mes:convertRecord:list")
    @GetMapping("/list")
    public TableDataInfo<PdfConvertRecordVo> list(PdfConvertRecordBo bo, PageQuery pageQuery) {
        return pdfConvertRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出PDF转换记录列表
     */
    @SaCheckPermission("mes:convertRecord:export")
    @Log(title = "PDF转换记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PdfConvertRecordBo bo, HttpServletResponse response) {
        List<PdfConvertRecordVo> list = pdfConvertRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "PDF转换记录", PdfConvertRecordVo.class, response);
    }

    /**
     * 获取PDF转换记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:convertRecord:query")
    @GetMapping("/{id}")
    public R<PdfConvertRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable("id") Long id) {
        return R.ok(pdfConvertRecordService.queryById(id));
    }

    /**
     * 新增PDF转换记录
     */
    @SaCheckPermission("mes:convertRecord:add")
    @Log(title = "PDF转换记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PdfConvertRecordBo bo) {
        return toAjax(pdfConvertRecordService.insertByBo(bo));
    }

    /**
     * 修改PDF转换记录
     */
    @SaCheckPermission("mes:convertRecord:edit")
    @Log(title = "PDF转换记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PdfConvertRecordBo bo) {
        return toAjax(pdfConvertRecordService.updateByBo(bo));
    }

    /**
     * 删除PDF转换记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:convertRecord:remove")
    @Log(title = "PDF转换记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(pdfConvertRecordService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 批量下载并转换MO或NO对应的文件为ZIP格式
     *
     * @param mos      MO号列表
     * @param nos      NO号列表
     * @param scale    缩略比例
     * @param response HTTP响应对象
     */
    @PostMapping("/batchDownloadAndConvertByMoOrNoWithZip")
    public void batchDownloadAndConvertByMoOrNoWithZip(
        @RequestParam(value = "mos", required = false) String mos,
        @RequestParam(value = "nos", required = false) String nos,
        @RequestParam(value = "scale", defaultValue = "0.94") float scale,
        HttpServletResponse response) {

        try {
            // 参数验证
            if ((mos == null || mos.trim().isEmpty()) && (nos == null || nos.trim().isEmpty())) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"至少需要提供一个图号号\"}");
                return;
            }

            // 缩放比例验证
            if (scale <= 0 || scale > 1.0f) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"缩放比例必须在0到1.0之间\"}");
                return;
            }

            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"converted_files.zip\"");
            response.setHeader("Cache-Control", "no-cache");

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                Set<String> addedFiles = new HashSet<>();
                List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

                // 处理MO号列表
                if (mos != null && !mos.trim().isEmpty()) {
                    String[] moArray = mos.split(",");
                    for (String mo : moArray) {
                        String cleanMo = mo != null ? mo.trim() : "";
                        if (!cleanMo.isEmpty()) {
                            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    return pdfConvertRecordService.processFile(cleanMo, "MO", scale);
                                } catch (Exception e) {
                                    log.error("处理MO号 {} 时发生错误: {}", cleanMo, e.getMessage());
                                    Map<String, Object> errorResult = new HashMap<>();
                                    errorResult.put("success", false);
                                    errorResult.put("identifier", cleanMo);
                                    errorResult.put("type", "MO");
                                    errorResult.put("error", e.getMessage());
                                    return errorResult;
                                }
                            }, executorService);
                            futures.add(future);
                        }
                    }
                }

                // 处理NO号列表
                if (nos != null && !nos.trim().isEmpty()) {
                    String[] noArray = nos.split(",");
                    for (String no : noArray) {
                        String cleanNo = no != null ? no.trim() : "";
                        if (!cleanNo.isEmpty()) {
                            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    return pdfConvertRecordService.processFile(cleanNo, "NO", scale);
                                } catch (Exception e) {
                                    log.error("处理NO号 {} 时发生错误: {}", cleanNo, e.getMessage());
                                    Map<String, Object> errorResult = new HashMap<>();
                                    errorResult.put("success", false);
                                    errorResult.put("identifier", cleanNo);
                                    errorResult.put("type", "NO");
                                    errorResult.put("error", e.getMessage());
                                    return errorResult;
                                }
                            }, executorService);
                            futures.add(future);
                        }
                    }
                }

                // 等待所有任务完成，设置超时时间
                try {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(5, java.util.concurrent.TimeUnit.MINUTES); // 5分钟超时
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("批量处理超时");
                    // 取消未完成的任务
                    futures.forEach(future -> future.cancel(true));
                }

                // 收集成功处理的文件并添加到ZIP
                int successCount = 0;
                int failCount = 0;
                Set<String> duplicateNames = new HashSet<>();

                for (CompletableFuture<Map<String, Object>> future : futures) {
                    try {
                        if (future.isDone() && !future.isCancelled()) {
                            Map<String, Object> result = future.get();
                            if (result != null) {
                                if ((Boolean) result.getOrDefault("success", false)) {
                                    // 处理重复文件名
                                    String originalName = (String) result.get("zipEntryName");
                                    String uniqueName = generateUniqueFileName(originalName, addedFiles, duplicateNames);
                                    result.put("zipEntryName", uniqueName);

                                    pdfConvertRecordService.addFileToZip(result, zipOut, addedFiles);

                                    // 保存转换记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存转换记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存转换记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    successCount++;
                                } else {
                                    // 保存失败记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存失败记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存失败记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    failCount++;
                                    log.warn("文件处理失败: {} - {}", result.get("identifier"), result.get("message"));
                                }
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("获取处理结果时发生错误", e);
                    }
                }

                // 添加处理结果摘要到ZIP
                String summary = String.format("处理完成: 成功 %d 个，失败 %d 个", successCount, failCount);
                zipOut.putNextEntry(new ZipEntry("处理结果.txt"));
                zipOut.write(summary.getBytes("UTF-8"));
                zipOut.closeEntry();

                log.info("批量处理完成: 成功 {} 个，失败 {} 个", successCount, failCount);
            }
        } catch (IOException e) {
            log.error("批量下载转换文件时发生IO错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            log.error("批量下载转换文件时发生未知错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
    }

    /**
     * 批量上传多文件图纸转换缩略后下载ZIP
     *
     * @param files    上传的PDF文件数组
     * @param scale    缩放比例，默认0.94
     * @param response HTTP响应
     */
    @PostMapping(value = "/batchUploadAndConvertWithZip", consumes = org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE)
    public void batchUploadAndConvertWithZip(
        @RequestParam("files") MultipartFile[] files,
        @RequestParam(value = "scale", defaultValue = "0.94") float scale,
        HttpServletResponse response) {

        try {
            // 参数验证
            if (files == null || files.length == 0) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"至少需要上传一个PDF文件\"}");
                return;
            }

            // 缩放比例验证
            if (scale <= 0 || scale > 1.0f) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"缩放比例必须在0到1.0之间\"}");
                return;
            }

            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"converted_uploaded_files.zip\"");
            response.setHeader("Cache-Control", "no-cache");

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                Set<String> addedFiles = new HashSet<>();
                List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
                Set<String> duplicateNames = new HashSet<>();

                // 处理每个上传的文件
                for (int i = 0; i < files.length; i++) {
                    MultipartFile file = files[i];
                    final int fileIndex = i; // 为闭包使用

                    // 跳过空文件
                    if (file.isEmpty()) {
                        log.warn("跳过空文件: {}", file.getOriginalFilename());
                        continue;
                    }

                    // 验证文件类型
                    String originalFilename = file.getOriginalFilename();
                    if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".pdf")) {
                        log.warn("跳过非PDF文件: {}", originalFilename);
                        continue;
                    }

                    CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                        try {
                            return processUploadedFile(file, fileIndex, scale);
                        } catch (Exception e) {
                            log.error("处理上传文件 {} 时发生错误: {}", originalFilename, e.getMessage());
                            Map<String, Object> errorResult = new HashMap<>();
                            errorResult.put("success", false);
                            errorResult.put("identifier", originalFilename);
                            errorResult.put("type", "UPLOAD");
                            errorResult.put("error", e.getMessage());
                            errorResult.put("originalFileName", originalFilename);
                            return errorResult;
                        }
                    }, executorService);
                    futures.add(future);
                }

                // 等待所有任务完成，设置超时时间
                try {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(10, java.util.concurrent.TimeUnit.MINUTES); // 10分钟超时
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("批量处理超时");
                    // 取消未完成的任务
                    futures.forEach(future -> future.cancel(true));
                }

                // 收集成功处理的文件并添加到ZIP
                int successCount = 0;
                int failCount = 0;

                for (CompletableFuture<Map<String, Object>> future : futures) {
                    try {
                        if (future.isDone() && !future.isCancelled()) {
                            Map<String, Object> result = future.get();
                            if (result != null) {
                                if ((Boolean) result.getOrDefault("success", false)) {
                                    // 处理重复文件名
                                    String originalName = (String) result.get("zipEntryName");
                                    String uniqueName = generateUniqueFileName(originalName, addedFiles, duplicateNames);
                                    result.put("zipEntryName", uniqueName);

                                    pdfConvertRecordService.addFileToZip(result, zipOut, addedFiles);

                                    // 保存转换记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存转换记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存转换记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    successCount++;
                                } else {
                                    // 保存失败记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存失败记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存失败记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    failCount++;
                                    log.warn("文件处理失败: {} - {}", result.get("identifier"), result.get("message"));
                                }
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("获取处理结果时发生错误", e);
                    }
                }

                // 添加处理结果摘要到ZIP
                String summary = String.format("处理完成: 成功 %d 个，失败 %d 个", successCount, failCount);
                zipOut.putNextEntry(new ZipEntry("处理结果.txt"));
                zipOut.write(summary.getBytes("UTF-8"));
                zipOut.closeEntry();

                log.info("批量上传文件处理完成: 成功 {} 个，失败 {} 个", successCount, failCount);
            }
        } catch (IOException e) {
            log.error("批量上传转换文件时发生IO错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            log.error("批量上传转换文件时发生未知错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
    }

    /**
     * 处理上传的PDF文件
     *
     * @param file      上传的文件
     * @param fileIndex 文件索引
     * @param scale     缩放比例
     * @return 处理结果
     */
    private Map<String, Object> processUploadedFile(MultipartFile file, int fileIndex, float scale) {
        Map<String, Object> result = new HashMap<>();
        String tempDir = null;
        String tempFilePath = null;
        String scaledFilePath = null;

        try {
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                originalFilename = "uploaded_file_" + fileIndex + ".pdf";
            }

            // 在执行 transferTo 之前获取原始文件大小，避免 Undertow 在移动临时文件后 getSize 抛出 NoSuchFileException
            long originalFileSize = 0L;
            try {
                originalFileSize = file.getSize();
            } catch (Exception ex) {
                log.warn("获取上传文件大小失败，将在保存后再计算: {}", ex.getMessage());
            }

            // 创建临时目录
            tempDir = System.getProperty("java.io.tmpdir") + File.separator + "pdf_upload_" + System.currentTimeMillis() + "_" + fileIndex;
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }

            // 保存上传文件到临时目录
            tempFilePath = tempDir + File.separator + originalFilename;
            File tempFile = new File(tempFilePath);
            file.transferTo(tempFile);

            // 生成输出文件名
            String baseName = originalFilename;
            int dotIndex = originalFilename.lastIndexOf('.');
            if (dotIndex > 0) {
                baseName = originalFilename.substring(0, dotIndex);
            }
            String scaledFileName = baseName + "_scaled.pdf";
            scaledFilePath = tempDir + File.separator + scaledFileName;

            // 执行PDF缩放
            boolean scaleSuccess = pdfConvertRecordService.scalePdfWithIText(tempFilePath, scaledFilePath, scale, false);

            if (!scaleSuccess) {
                result.put("success", false);
                result.put("message", "PDF缩放失败");
                result.put("identifier", originalFilename);
                result.put("type", "UPLOAD");
                result.put("originalFileName", originalFilename);
                return result;
            }

            // 验证缩放后的文件
            File scaledFile = new File(scaledFilePath);
            if (!scaledFile.exists() || scaledFile.length() == 0) {
                result.put("success", false);
                result.put("message", "缩放后的文件不存在或为空");
                result.put("identifier", originalFilename);
                result.put("type", "UPLOAD");
                result.put("originalFileName", originalFilename);
                return result;
            }

            // 如果之前未能获取原始大小，则使用保存后的源文件大小
            if (originalFileSize <= 0L && tempFile.exists()) {
                originalFileSize = tempFile.length();
            }

            // 成功处理
            result.put("success", true);
            result.put("identifier", originalFilename);
            result.put("type", "UPLOAD");
            result.put("originalFileName", originalFilename);
            result.put("scaledFilePath", scaledFilePath);
            result.put("zipEntryName", originalFilename);
            result.put("tempDir", tempDir);
            result.put("fileSize", scaledFile.length());
            result.put("originalFileSize", originalFileSize);
            result.put("scaledFileSize", scaledFile.length());

            log.info("成功处理上传文件: {} -> {}", originalFilename, originalFilename);
            return result;

        } catch (Exception e) {
            log.error("处理上传文件时发生错误: {}", e.getMessage(), e);
            // 避免在 Undertow 已移动/清理临时文件后继续访问 MultipartFile 导致的异常
            String safeOriginalName;
            try {
                safeOriginalName = file.getOriginalFilename();
            } catch (Exception ignore) {
                safeOriginalName = "unknown.pdf";
            }
            result.put("success", false);
            result.put("message", "处理上传文件时发生错误: " + e.getMessage());
            result.put("identifier", safeOriginalName);
            result.put("type", "UPLOAD");
            result.put("originalFileName", safeOriginalName);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * 生成唯一的文件名，避免重复
     */
    private String generateUniqueFileName(String originalName, Set<String> addedFiles, Set<String> duplicateNames) {
        if (originalName == null) {
            originalName = "unknown.pdf";
        }

        String baseName = originalName;
        String extension = "";
        int dotIndex = originalName.lastIndexOf('.');
        if (dotIndex > 0) {
            baseName = originalName.substring(0, dotIndex);
            extension = originalName.substring(dotIndex);
        }

        String uniqueName = originalName;
        int counter = 1;
        while (addedFiles.contains(uniqueName) || duplicateNames.contains(uniqueName)) {
            uniqueName = baseName + "_" + counter + extension;
            counter++;
        }

        duplicateNames.add(uniqueName);
        return uniqueName;
    }

    /**
     * 保存PDF转换记录到数据库
     */
    private void savePdfConvertRecord(Map<String, Object> result, float scale) {
        try {
            PdfConvertRecordBo bo = new PdfConvertRecordBo();

            // 设置基本信息
            String identifier = (String) result.get("identifier");
            String type = (String) result.get("type");

            if ("MO".equals(type)) {
                bo.setMoNumber(identifier);
                // MO类型时，NO号可以为空
                bo.setNoNumber(null);
            } else if ("NO".equals(type)) {
                bo.setNoNumber(identifier);
                // 如果是NO类型，MO号设置为空字符串以满足非空约束，但不使用NO号值
                bo.setMoNumber("");
            } else if ("UPLOAD".equals(type)) {
                // 前端上传的文件没有MO/NO，保持NO为空，MO设为空字符串以满足数据库非空约束
                bo.setMoNumber("");
                bo.setNoNumber(null);
            }

            // 设置文件信息
            bo.setOriginalFileName((String) result.get("originalFileName"));
            bo.setConvertedFileName((String) result.get("zipEntryName"));

            // 设置处理状态
            Boolean success = (Boolean) result.getOrDefault("success", false);
            bo.setStatus(success ? 1L : 2L); // 1-成功，2-失败

            // 设置错误信息（如果有）
            if (!success) {
                String errorMessage = (String) result.get("message");
                if (errorMessage == null) {
                    errorMessage = (String) result.get("error");
                }
                bo.setErrorMessage(errorMessage);
            }

            // 设置文件大小
            Object fileSizeObj = result.get("fileSize");
            if (fileSizeObj != null) {
                if (fileSizeObj instanceof Long) {
                    bo.setFileSize((Long) fileSizeObj);
                } else if (fileSizeObj instanceof Integer) {
                    bo.setFileSize(((Integer) fileSizeObj).longValue());
                } else if (fileSizeObj instanceof String) {
                    try {
                        bo.setFileSize(Long.parseLong((String) fileSizeObj));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析文件大小: {}", fileSizeObj);
                    }
                }
            }

            // 保存到数据库
            pdfConvertRecordService.insertByBo(bo);

        } catch (Exception e) {
            log.error("保存PDF转换记录时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

}
