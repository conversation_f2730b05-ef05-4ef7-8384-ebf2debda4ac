package org.dromara.mes.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.mes.domain.OfficeUser;

/**
 * 报工用户业务对象 office_user
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OfficeUser.class, reverseConvertGenerate = false)
public class OfficeUserBo extends BaseEntity {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 工号
     */
    @NotBlank(message = "工号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workNum;

    /**
     * 工作人员
     */
    @NotBlank(message = "工作人员不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 联系电话
     */
    private String userPhone;

    /**
     * 办事处代码
     */
    private String officeLocId;

    /**
     * 办事处名称
     */
    private String officeLocName;

    /**
     * 负责人姓名
     */
    private String officeSupervisor;


}
