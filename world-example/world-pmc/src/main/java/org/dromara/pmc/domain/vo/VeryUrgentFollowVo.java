package org.dromara.pmc.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import org.apache.commons.collections4.ListUtils;

import java.util.List;
import java.util.Objects;

/**
 * 特急件跟进表
 */
@Data
public class VeryUrgentFollowVo {
    /**
     * 跟单人
     */
    @ExcelProperty(value = "跟单人")
    private String merchandiser;

    /**
     * 特急件图纸数
     */
    @ExcelProperty(value = "特急件图纸数")
    private Integer totalPapers;

    private List<String> totalPaperDetails;

    /**
     * 交期达成
     */
    @ExcelProperty(value = "交期达成")
    private Integer finishedTotalPapers;

    private List<String> finishedTotalPaperDetails;

    /**
     * 延误项数
     */
    @ExcelProperty(value = "延误项数")
    private Integer delayTotalPapers;

    private List<String> delayTotalPaperDetails;

    /**
     * 交货达成率
     */
    @ExcelProperty(value = "交货达成率")
    private Double totalRate;

    /**
     * 生产
     */
    @ExcelProperty(value = "生产")
    private Integer totalProductionPapers;

    private List<String> totalProductionPaperDetails;

    /**
     * 生产交期达成
     */
    @ExcelProperty(value = "生产交期达成")
    private Integer finishedProductionPapers;

    private List<String> finishedProductionPaperDetails;

    /**
     * 生产延误项数
     */
    @ExcelProperty(value = "生产延误项数")
    private Integer delayProductionPapers;

    private List<String> delayProductionPaperDetails;

    /**
     * 生产交货达成率
     */
    @ExcelProperty(value = "生产交货达成率")
    private Double productionRate;

    /**
     * 钣金
     */
    @ExcelProperty(value = "钣金")
    private Integer totalMetalPlatePapers;

    private List<String> totalMetalPlatePaperDetails;

    /**
     * 钣金交期达成
     */
    @ExcelProperty(value = "钣金交期达成")
    private Integer finishedMetalPlatePapers;

    private List<String> finishedMetalPlatePaperDetails;

    /**
     * 钣金延误项数
     */
    @ExcelProperty(value = "钣金延误项数")
    private Integer delayMetalPlatePapers;

    private List<String> delayMetalPlatePaperDetails;

    /**
     * 钣金交货达成率
     */
    @ExcelProperty(value = "钣金交货达成率")
    private Double metalPlateRate;

    /**
     * 外发
     */
    @ExcelProperty(value = "外发")
    private Integer totalDeliveryPapers;

    private List<String> totalDeliveryPaperDetails;

    /**
     * 外发交期达成
     */
    @ExcelProperty(value = "外发交期达成")
    private Integer finishedDeliveryPapers;

    private List<String> finishedDeliveryPaperDetails;

    /**
     * 外发延误项数
     */
    @ExcelProperty(value = "外发延误项数")
    private Integer delayDeliveryPapers;

    private List<String> delayDeliveryPaperDetails;

    /**
     * 外发交货达成率
     */
    @ExcelProperty(value = "外发交货达成率")
    private Double deliveryRate;

    /**
     * QA
     */
    @ExcelProperty(value = "QA")
    private Integer totalQAPapers;

    private List<String> totalQAPaperDetails;

    /**
     * QA交期达成
     */
    @ExcelProperty(value = "QA交期达成")
    private Integer finishedQAPapers;

    private List<String> finishedQAPaperDetails;

    /**
     * QA延误项数
     */
    @ExcelProperty(value = "QA延误项数")
    private Integer delayQAPapers;

    private List<String> delayQAPaperDetails;

    /**
     * QA交货达成率
     */
    @ExcelProperty(value = "QA交货达成率")
    private Double qARate;

    public List<String> getDelayTotalPaperDetails() {
        return ListUtils.subtract(
            Objects.requireNonNullElse(totalPaperDetails, List.of()),
            Objects.requireNonNullElse(finishedTotalPaperDetails, List.of())
        );
    }

    public List<String> getFinishedProductionPaperDetails() {
        return ListUtils.subtract(
            Objects.requireNonNullElse(totalProductionPaperDetails, List.of()),
            Objects.requireNonNullElse(delayProductionPaperDetails, List.of())
        );
    }

    public List<String> getFinishedMetalPlatePaperDetails() {
        return ListUtils.subtract(
            Objects.requireNonNullElse(totalMetalPlatePaperDetails, List.of()),
            Objects.requireNonNullElse(delayMetalPlatePaperDetails, List.of())
        );
    }

    public List<String> getFinishedDeliveryPaperDetails() {
        return ListUtils.subtract(
            Objects.requireNonNullElse(totalDeliveryPaperDetails, List.of()),
            Objects.requireNonNullElse(delayDeliveryPaperDetails, List.of())
        );
    }

    public List<String> getFinishedQAPaperDetails() {
        return ListUtils.subtract(
            Objects.requireNonNullElse(totalQAPaperDetails, List.of()),
            Objects.requireNonNullElse(delayQAPaperDetails, List.of())
        );
    }

    public Integer getDelayTotalPapers() {
        if (totalPapers == null || finishedTotalPapers == null) {
            return 0;
        }
        return totalPapers - finishedTotalPapers;
    }

    public Integer getFinishedProductionPapers() {
        if (totalProductionPapers == null || delayProductionPapers == null) {
            return 0;
        }
        return totalProductionPapers - delayProductionPapers;
    }

    public Integer getFinishedMetalPlatePapers() {
        if (totalMetalPlatePapers == null || delayMetalPlatePapers == null) {
            return 0;
        }
        return totalMetalPlatePapers - delayMetalPlatePapers;
    }

    public Integer getFinishedDeliveryPapers() {
        if (totalDeliveryPapers == null || delayDeliveryPapers == null) {
            return 0;
        }
        return totalDeliveryPapers - delayDeliveryPapers;
    }

    public Integer getFinishedQAPapers() {
        if (totalQAPapers == null || delayQAPapers == null) {
            return 0;
        }
        return totalQAPapers - delayQAPapers;
    }

    public Double getTotalRate() {
        if (totalPapers == null || totalPapers == 0 || finishedTotalPapers == null) {
            return null;
        }
        return (double) finishedTotalPapers / totalPapers * 100;
    }

    public Double getProductionRate() {
        if (totalProductionPapers == null || totalProductionPapers == 0 || getFinishedProductionPapers() == null) {
            return null;
        }
        return (double) getFinishedProductionPapers() / totalProductionPapers * 100;
    }

    public Double getMetalPlateRate() {
        if (totalMetalPlatePapers == null || totalMetalPlatePapers == 0 || getFinishedMetalPlatePapers() == null) {
            return null;
        }
        return (double) getFinishedMetalPlatePapers() / totalMetalPlatePapers * 100;
    }

    public Double getDeliveryRate() {
        if (totalDeliveryPapers == null || totalDeliveryPapers == 0 || getFinishedDeliveryPapers() == null) {
            return null;
        }
        return (double) getFinishedDeliveryPapers() / totalDeliveryPapers * 100;
    }

    public Double getQARate() {
        if (totalQAPapers == null || totalQAPapers == 0 || getFinishedQAPapers() == null) {
            return null;
        }
        return (double) getFinishedQAPapers() / totalQAPapers * 100;
    }
}
