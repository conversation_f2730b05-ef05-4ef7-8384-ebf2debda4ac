package org.dromara.pmc.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.pmc.domain.ProcessStatisticsQueryDTO;
import org.dromara.pmc.domain.WdzdhSbjdb;
import org.dromara.pmc.domain.bo.DrPair;
import org.dromara.pmc.domain.vo.ProcessDetailVo;
import org.dromara.pmc.domain.vo.ProcessStatisticsVo;
import org.dromara.pmc.domain.vo.ResponsiblePerson;
import org.dromara.pmc.domain.vo.WdzdhSbjdbVo;

import java.util.List;

/**
 * 流程文件管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@InterceptorIgnore(tenantLine = "true")
@DS("sqlserver1")
public interface ProcessStatisticsMapper  {

    /**
     * 查询单工序统计列表
     * @param param 查询条件
     * @return 流程统计列表
     */
    List<ProcessStatisticsVo> listProcessStatistics(@Param("param") ProcessStatisticsQueryDTO param);

    /**
     * 查询外发单工序加工明细列表
     * @param param 查询条件
     * @return 外发加工明细列表
     */
    List<ProcessDetailVo> listProcessDetail(@Param("param") ProcessStatisticsQueryDTO param);

    /**
     * 查询全工序统计列表
     * @param param 查询条件
     * @return 全工序统计列表
     */
    List<ProcessStatisticsVo> listEntireProcessStatistics(@Param("param") ProcessStatisticsQueryDTO param);

    /**
     * 查询外发全工序加工明细列表
     * @param param 查询条件
     * @return 外发加工明细列表
     */
    List<ProcessDetailVo> listEntireProcessDetail(@Param("param") ProcessStatisticsQueryDTO param);

}

