package org.dromara.pmc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.Data;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.VeryUrgentFollowDetail;
import org.dromara.pmc.domain.vo.VeryUrgentDetailVo;
import org.dromara.pmc.domain.vo.VeryUrgentFollowVo;
import org.dromara.pmc.service.VeryUrgentFollowService;
import org.dromara.pmc.utils.SqlserverJdbcUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.dromara.pmc.utils.RowMapperUtil.*;

@Service
public class VeryUrgentFollowServiceImpl implements VeryUrgentFollowService {
    @Override
    public List<VeryUrgentFollowVo> getStatistics(PmcSearchForm form) {
        var tempDataTable = "##ZNZB_PMC_tjjTJ_temp" + RandomUtil.randomInt(10000, 99999);
        var tempProcessDataTable = "##ZNZB_PMC_tjjTJMO_temp" + RandomUtil.randomInt(10000, 99999);
        var tempDataDetailTable = "##ZNZB_PMC_tjjTJ_detail_temp" + RandomUtil.randomInt(10000, 99999);
//        var tempDataTable = "##ZNZB_PMC_tjjTJ_temp";
//        var tempProcessDataTable = "##ZNZB_PMC_tjjTJMO_temp";
//        var tempDataDetailTable = "##ZNZB_PMC_tjjTJ_detail_temp";
        List<Object> params = new ArrayList<>();
        var sql = String.join("\n",
            getDeleteTableSql(tempDataTable, tempProcessDataTable, tempDataDetailTable),
            getCreateDetailTableSql(tempDataDetailTable),
            getInitDataTableQuery(tempDataTable),
            getDataSql(form, tempProcessDataTable, params),
            getUpdateDataSql(tempProcessDataTable),
            getCalculateDataSql(tempDataTable, tempProcessDataTable, tempDataDetailTable),
            getSelectSql(tempDataTable)
        );
//        System.out.println(sql);
        try {
            SqlserverJdbcUtils.switchDataSource("sqlserver1");
            var data = SqlserverJdbcUtils.executeQuery(
                sql, getVeryUrgentFollowVoRowMapper(), params
            );
            var detailData = SqlserverJdbcUtils.executeQuery(
                getSelectDetailSql(tempDataDetailTable),
                getVeryUrgentFollowVoDetailRowMapper(),
                null
            );
            // 删除临时表
            SqlserverJdbcUtils.executeUpdate(getDeleteTableSql(
                tempDataTable, tempProcessDataTable, tempDataDetailTable
            ), null);
            populateDetailData(data, detailData);
            calculateTotal(data);
            return data;
        } catch (SQLException e) {
            throw new RuntimeException("查询失败", e);
        }
    }

    private List<VeryUrgentDetailVo> getDetailsInternal(List<String> moNos) {
        if (ObjectUtil.isEmpty(moNos)) {
            return List.of();
        }
        var placements = IntStream.rangeClosed(1, moNos.size())
            .boxed().map(integer -> "?")
            .collect(Collectors.joining(", "));
        List<Object> params = new ArrayList<>(moNos.stream().map(String::trim).toList());
        var sql = """
            SELECT ID, 状态, 生产车间 + ' ' + 制造部门 as 制造部门, SO_NO, SO_ITM, MO_NO, 图号, 版本,
            客户代码, 订单数量, 生产数量, 接单日期, 客户要求交期, PMC要求交期, cast('' as smalldatetime) as 入仓日期,
            末工序时间, 0 as 延误天数, 客户PO号, 备注, 下单员姓名 as 下单员, 跟单人 as 跟单负责人, 工艺编制人,
            BOM责任人, 项目经理, 分组时间, 利润中心, 零件分类, URGENT, 订单类别, 类别描述, 计划类型, 品号, 内部订单号,
            上层订单号, DR_NO, DR_ITM, 动作, 生产车间
            FROM [master] AS A with(nolock)
            where MO_NO in (_placements_);
            """.replace("_placements_", placements);
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        try {
            return SqlserverJdbcUtils.executeQuery(
                sql, getVeryUrgentDetailVoRowMapper(), params
            );
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<VeryUrgentDetailVo> getDetails(List<String> moNos) {
        if (ObjectUtil.isEmpty(moNos)) {
            return List.of();
        }
        var result = new ArrayList<VeryUrgentDetailVo>();
        var size = 900;
        for (int i = 0; i < moNos.size(); i += size) {
            var subList = moNos.subList(i, Math.min(i + size, moNos.size()));
            result.addAll(getDetailsInternal(subList));
        }
        return result;
    }

    private void populateDetailData(List<VeryUrgentFollowVo> data, List<VeryUrgentFollowDetail> detailData) {
        for (var vo : data) {
            var target = detailData.stream().filter(
                d -> Objects.equals(d.getMerchandiser(), vo.getMerchandiser())
            ).toList();
            vo.setTotalPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "特急件图纸数")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setFinishedTotalPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "交期达成")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setDelayTotalPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "延误项数")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setTotalProductionPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "生产")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setDelayProductionPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "生产延误项数")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setTotalMetalPlatePaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "钣金")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setDelayMetalPlatePaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "钣金延误项数")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setTotalDeliveryPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "外发")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setDelayDeliveryPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "外发延误项数")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setTotalQAPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "QA")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
            vo.setDelayQAPaperDetails(target.stream().filter(
                d -> Objects.equals(d.getPaperType(), "QA延误项数")
            ).map(VeryUrgentFollowDetail::getMoNo).toList());
        }
    }

    private void calculateTotal(List<VeryUrgentFollowVo> data) {
        var total = new VeryUrgentFollowVo();
        var totalPapers = 0;
        var finishedTotalPapers = 0;
        var delayTotalPapers = 0;
        var totalProductionPapers = 0;
        var totalMetalPlatePapers = 0;
        var totalDeliveryPapers = 0;
        var totalQAPapers = 0;

        for (var vo : data) {
            totalPapers += Objects.requireNonNullElse(vo.getTotalPapers(), 0);
            finishedTotalPapers += Objects.requireNonNullElse(vo.getFinishedTotalPapers(), 0);
            delayTotalPapers += Objects.requireNonNullElse(vo.getDelayTotalPapers(), 0);
            totalProductionPapers += Objects.requireNonNullElse(vo.getTotalProductionPapers(), 0);
            totalMetalPlatePapers += Objects.requireNonNullElse(vo.getTotalMetalPlatePapers(), 0);
            totalDeliveryPapers += Objects.requireNonNullElse(vo.getTotalDeliveryPapers(), 0);
            totalQAPapers += Objects.requireNonNullElse(vo.getTotalQAPapers(), 0);
        }

        total.setMerchandiser("合计");
        total.setTotalPapers(totalPapers);
        total.setFinishedTotalPapers(finishedTotalPapers);
        total.setDelayTotalPapers(delayTotalPapers);
        total.setTotalRate(totalPapers == 0 ? 0 : (double) finishedTotalPapers / totalPapers * 100);
        total.setTotalProductionPapers(totalProductionPapers);
        total.setTotalMetalPlatePapers(totalMetalPlatePapers);
        total.setTotalDeliveryPapers(totalDeliveryPapers);
        total.setTotalQAPapers(totalQAPapers);

        data.add(total);
    }

    private String getCreateDetailTableSql(String dataDetailTable) {
        return """
            create table %s (
                跟单负责人 nvarchar(20),
                paper_type nvarchar(20),
                mo_no nvarchar(50)
            );
            """.formatted(dataDetailTable);
    }

    private String getSelectSql(String dataTable) {
        return "select * from %s;".formatted(dataTable);
    }

    private String getSelectDetailSql(String detailTable) {
        return "select * from %s;".formatted(detailTable);
    }

    private String getDeleteTableSql(String dataTable, String processDataTable, String dataDetailTable) {
        return """
            IF OBJECT_ID('tempdb.._data_table_', 'U') IS NOT NULL DROP TABLE _data_table_;
            IF OBJECT_ID('tempdb.._process_table_', 'U') IS NOT NULL DROP TABLE _process_table_;
            IF OBJECT_ID('tempdb.._data_detail_table_', 'U') IS NOT NULL DROP TABLE _data_detail_table_;
            """.replace("_data_table_", dataTable)
            .replace("_process_table_", processDataTable)
            .replace("_data_detail_table_", dataDetailTable);
    }

    private String getInitDataTableQuery(String dataTable) {
        return """
            SELECT ID, 工程师, 特急件图纸数, 交期达成, 延误项数, 交货达成率, 生产, 生产延误项数,
            生产达成率, 钣金, 钣金延误项数, 钣金达成率, 外发, 外发延误项数, 外发达成率, QA, QA延误项数, QA达成率
            INTO %s
            FROM ZNZB_PMC_TJJ with(nolock)
            where isnull(调动, '') = ''
            order by id;
            """.formatted(dataTable);
    }

    private String getDataSql(PmcSearchForm form, String processTable, List<Object> params) {
        var sql = """
            SELECT ID, 状态, 生产车间 + ' ' + 制造部门 as 制造部门, SO_NO, SO_ITM, MO_NO, 图号, 版本,
            客户代码, 订单数量, 生产数量, 接单日期, 客户要求交期, PMC要求交期, cast('' as smalldatetime) as 入仓日期,
            末工序时间, 0 as 延误天数, 客户PO号, 备注, 下单员姓名 as 下单员, 跟单人 as 跟单负责人, 工艺编制人,
            BOM责任人, 项目经理, 分组时间, 利润中心, 零件分类, URGENT, 订单类别, 类别描述, 计划类型, 品号, 内部订单号,
            上层订单号, DR_NO, DR_ITM, 动作, 生产车间
            INTO _process_table_
            FROM [master] AS A with(nolock)
            where 接单日期 BETWEEN ? and ?
                and (接单日期 >= '2024-03-01 00:00')
                AND A.状态 <> 'SO单'
            --    AND (
            --        (
            --            isnull(A.状态1, 0) < 2
            --            or isnull(A.状态1, 0) > 8
            --        )
            --        AND (
            --            not (
            --                isnull(A.状态1, 0) > 1
            --                and isnull(A.状态1, 0) < 9
            --                and a.状态 in('关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件仓','进多余半成品仓','进待利用仓','SO单','报废','出货','领出','开单领出','转关单','虚拟入库发料','成品报废','进成品仓','进半成品仓','包装','转半成品仓','钣金转焊接')
            --            )
            --        )
            --    )
                and (
                    CHARINDEX('P', A.MO_NO, 3) = 0
                    AND CHARINDEX('N', A.MO_NO, 3) = 0
                )
                AND ISNULL(A.MO_NO, '') <> ''
                and (ISNULL(A.项目类型, '') <> '40')
                AND (left(A.品号,1)='8')
                AND ISNULL(A.URGENT, '') <> ''
                and not (备注 like '%标签%' or 图号 like '%标签%')
                and not (isnull(生产车间,'')='BUA' or isnull(生产车间,'')='BUB' or isnull(生产车间,'')='PMD')
            """.replace("_process_table_", processTable);
        params.add(form.getBargainDate().get(0));
        params.add(form.getBargainDate().get(1));

        if (ObjectUtil.isNotEmpty(form.getCustomerCode())) {
            sql += " AND 客户代码 = ? ";
            params.add(form.getCustomerCode());
        }
        if (ObjectUtil.isNotEmpty(form.getMoNumber())) {
            sql += " AND MO_NO = ? ";
            params.add(form.getMoNumber());
        }
        if (ObjectUtil.isNotEmpty(form.getMerchandiser())) {
            sql += " AND 跟单负责人 = ? ";
            params.add(form.getMerchandiser());
        }
        if (ObjectUtil.isNotEmpty(form.getPmcReqDate()) && form.getPmcReqDate().size() == 2) {
            sql += " AND PMC要求交期 BETWEEN ? and ? ";
            params.add(form.getPmcReqDate().get(0));
            params.add(form.getPmcReqDate().get(1));
        }
        // 未完成
        if ("unfinished".equals(form.getStatus())) {
            sql += """
                and 状态 not in('删除','出货','领出','关闭','OK','进半成品仓','虚拟入库发料','转半成品仓',
                '外发收货送检','多余件领出','关单','取消','D/N','多余件领出','进多余件仓','进多余半成品仓','进待利用仓',
                'SO单','报废','开单领出','转关单','成品报废','进成品仓','进半成品仓','包装','转半成品仓','钣金转焊接')
                """;
        }
        return sql + ";";
    }

    private String getUpdateDataSql(String processTable) {
        return """
            update a
            set a.入仓日期 = b.开始时间
            FROM _table_name_ AS A,PP_SCAN AS B WITH(NOLOCK)
            WHERE A.MO_NO=B.PJ号 and b.动作 like '%收货送检' and a.计划类型 in('1','U','5','6','7');
            update a
            set a.入仓日期 = b.开始时间
            FROM _table_name_ AS A,PP_SCAN AS B WITH(NOLOCK)
            WHERE A.MO_NO=B.PJ号 and b.动作='QA收货MFG' and a.计划类型='2';
            update a
            set a.入仓日期 = b.开始时间
            FROM _table_name_ AS A,PP_SCAN AS B WITH(NOLOCK) WHERE A.MO_NO=B.PJ号 and b.动作='包装' and a.计划类型='3';
            update a
            set a.入仓日期 = null,
                延误天数 = 0
            FROM _table_name_ AS A where a.入仓日期='1900-01-01 00:00:00' OR a.入仓日期<'1999-01-01 00:00:00';
            update a
            set a.入仓日期 = B.开始时间,
                a.延误天数 = DATEDIFF(DD, a.PMC要求交期, b.开始时间)
            FROM _table_name_ AS A,pp_scan as b with(nolock)
            where A.MO_NO=B.PJ号 and b.动作='包装' and A.零件分类='P' and (a.入仓日期 is null or a.入仓日期<'1999-01-01 00:00:00');
            update a
            set a.延误天数 = DATEDIFF(DD, A.PMC要求交期, A.入仓日期)
            from _table_name_ as a where a.入仓日期<>'';
            update a
            set a.延误天数 = DATEDIFF(
                DD,
                A.PMC要求交期,
                Format(GETDATE(), 'yyyy-MM-dd')
            )
            from _table_name_ as a where isnull(a.入仓日期,'')='';
            update a
            set a.制造部门 = LTRIM(制造部门)
            from _table_name_ as a where a.制造部门<>'';
            update a
            set a.制造部门 = RTRIM(LEFT(制造部门, LEN(制造部门) -1))
            from _table_name_ as a where right(a.制造部门,1) in('1','2','3','5','6','7','Y','U','V');

            update _table_name_
            set 跟单负责人 = '空'
            where 跟单负责人 is NULL;
            """.replace("_table_name_", processTable);
    }

    private String getCalculateDataSql(String dataTable, String processDataTable, String dataDetailTable) {
        var totalDto = new Dto(dataTable, processDataTable, dataDetailTable, "特急件图纸数", "交期达成");
        var productionDto = new Dto(dataTable, processDataTable, dataDetailTable, "生产", "生产延误项数");
        productionDto.setFilter("""
            AND (计划类型='2') and (
                动作 in('图纸分组','工艺','草稿工艺','工艺退图','工艺问图','产生MO单','MRP','排产','待退生产部','外发转内部返修',
                    '已退生产','已转生产部','转生产部','待退原料仓','标准件仓退图纸','材料仓收图','待买料', '发料',
                    '热处理中心转总厂','转料','总厂转热处理中心','钣金分厂转总厂','待工装','待铜公','转生产部', 'CNC编程','IPQC放行',
                    'IPQC检测','IPQC退货','IPQC退货检测','QA退货检测','QA退货收料','待检', '返修','工艺反馈','加工','取消收料',
                    '取消退料','生产转QA','收料','收图','退QA','退料','暂停单退料','暂停收料', '转热处理中心','工艺反馈完成',
                    '总厂转钣金分厂','已更新图纸','待更新图纸','CNC收图','CNC问图','待CMM检测','待处理','待MRB签单','调机')
                or 状态 in('关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件成品仓','进多余半成品仓','进待利用仓',
                    'SO单','报废','出货','领出','开单领出','转关单','虚拟入库发料','转半成品仓','进半成品仓','进多余半成品仓',
                    '退半成品仓','半成品多余件包装入库','半成品仓收图')
            )
            """);
        var metalPlateDto = new Dto(dataTable, processDataTable, dataDetailTable, "钣金", "钣金延误项数");
        metalPlateDto.setFilter("""
            AND (计划类型='3') and (
                动作 in('钣金QA检测','待退钣金部','转钣金部','总厂转钣金分厂','钣金绘图完成','钣金收料','钣金收图','钣金转焊接',
                    '待CMM检测','待MRB签单','待处理','待检','加工','材料进仓')
                or (状态 like '%钣金%')
                or 状态 in ('关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件成品仓','进多余半成品仓','进待利用仓',
                    'SO单','报废','出货','领出','开单领出','转关单','虚拟入库发料','转半成品仓','进半成品仓','进多余半成品仓',
                    '退半成品仓','半成品多余件包装入库','半成品仓收图')
            )
            """);
        var deliveryDto = new Dto(dataTable, processDataTable, dataDetailTable, "外发", "外发延误项数");
        deliveryDto.setFilter("""
            AND (计划类型 in ('1','U','5','6','7')) and (
                动作 in ('托工','钣金分厂转序','钣金分厂转总厂','供应商欠数','外发退货','外发转序','已转外发仓','钣金分厂待外发',
                    '待退供应商','待外发','供料待发','供料已发','供应商退回','退供应商','外发','装配待退供应商','装配退供应商',
                    '托工厂商登记','返回PMC确认','取消供料','外发反馈','外发更新待收旧图','外发更新退回','外发更新完成','外协询价',
                    '已发供料尺寸','已合并供料','外发核价完成')
                or (状态 like '%外发')
                or 状态 in ('关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件成品仓','进多余半成品仓','进待利用仓',
                    'SO单','报废','出货','领出','开单领出','转关单','虚拟入库发料','转半成品仓','进半成品仓','进多余半成品仓',
                    '退半成品仓','半成品多余件包装入库','半成品仓收图','外发转内部返修')
            )
            """);
        var qaDto = new Dto(dataTable, processDataTable, dataDetailTable, "QA", "QA延误项数");
        qaDto.setFilter("""
            and (
                动作 in ('CMM已检测','CMM已检测MFG','MRB处理','QA半工序收货','QA检验结束','QA检验开始','QA收货MFG','QA收货WF',
                    'QA转装配','待MRB','待MRB会议','待SQE处理','待SQE确认','待工艺处理','待退生产部','待退原料仓','单工序QA检测',
                    '电镀收货','供应商欠数','退标准件仓','外发收货送检','外发检测','外发退货','外发转内部返修','外发转序','转CMM',
                    '转钣金部','转刻字','刻字收货','转生产部','转装配','总厂转钣金分厂','电镀热处理收货送检','退QA','转QA','装配退QA')
                and 状态 not in('关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件成品仓','进多余半成品仓',
                    '进待利用仓','SO单','报废','出货','领出','开单领出','转关单','虚拟入库发料','转半成品仓','进半成品仓',
                    '进多余半成品仓','退半成品仓','半成品多余件包装入库','半成品仓收图','外发转内部返修')
            )
            """);
        return String.join("\n",
            getSpecificItemSql(totalDto),
            getSpecificItemSql(productionDto),
            getSpecificItemSql(metalPlateDto),
            getSpecificItemSql(deliveryDto),
            getSpecificItemSql(qaDto)
        );
    }

    private String getSpecificItemSql(Dto dto) {
        return """
            insert into _data_detail_table_
            select 跟单负责人, '_total_item_', mo_no
            from _process_table_
            where mo_no != '' _filter_;

            update _data_table_
            set _total_item_ = t.value
            from (
            	SELECT COUNT(*) as value, 跟单负责人
            	from _data_detail_table_
            	where paper_type = '_total_item_'
            	group by 跟单负责人
            ) t
            where 工程师 = t.跟单负责人;

            insert into _data_detail_table_
            select 跟单负责人, '_delay_item_', mo_no
            from _process_table_
            where mo_no != '' AND (isnull(延误天数,0)>0) _filter_;

            update _data_table_
            set _delay_item_ = t.value
            from (
            	SELECT COUNT(*) as value, 跟单负责人
            	from _data_detail_table_
            	where paper_type = '_delay_item_'
            	group by 跟单负责人
            ) t
            where 工程师 = t.跟单负责人;
            """.replace("_data_table_", dto.getDataTable())
            .replace("_process_table_", dto.getProcessTable())
            .replace("_data_detail_table_", dto.getDataDetailTable())
            .replace("_total_item_", dto.getTotalItemField())
            .replace("_delay_item_", dto.getDelayItemField())
            .replace("_filter_", dto.getFilter());
    }

    @Data
    private static class Dto {
        private String dataTable;
        private String processTable;
        private String dataDetailTable;
        private String totalItemField;
        private String delayItemField;
        private String filter;

        public Dto(String dataTable, String processTable, String dataDetailTable, String totalItemField, String delayItemField) {
            this.dataTable = dataTable;
            this.processTable = processTable;
            this.dataDetailTable = dataDetailTable;
            this.totalItemField = totalItemField;
            this.delayItemField = delayItemField;
            filter = "";
        }
    }
}
