package org.dromara.pmc.domain.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class VeryUrgentDetailVo {
    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 制造部门
     */
    @ExcelProperty(value = "制造部门")
    private String manufacturingDepartment;

    /**
     * SO_NO
     */
    @ExcelProperty(value = "SO_NO")
    private String soNo;

    /**
     * SO_ITM
     */
    @ExcelProperty(value = "SO_ITM")
    private String soItm;

    /**
     * MO_NO
     */
    @ExcelProperty(value = "MO_NO")
    private String moNo;

    /**
     * 图号
     */
    @ExcelProperty(value = "图号")
    private String drawingNumber;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String customerCode;

    /**
     * 订单数量
     */
    @ExcelProperty(value = "订单数量")
    private Integer orderQuantity;

    /**
     * 生产数量
     */
    @ExcelProperty(value = "生产数量")
    private Integer productionQuantity;

    /**
     * 接单日期
     */
    @ExcelProperty(value = "接单日期")
    private Date orderDate;

    /**
     * 客户要求交期
     */
    @ExcelProperty(value = "客户要求交期")
    private Date customerRequiredDeliveryDate;

    /**
     * PMC要求交期
     */
    @ExcelProperty(value = "PMC要求交期")
    private Date pmcRequiredDeliveryDate;

    /**
     * 入仓日期
     */
    @ExcelProperty(value = "入仓日期")
    private Date warehouseEntryDate;

    /**
     * 末工序时间
     */
    @ExcelProperty(value = "末工序时间")
    private Date finalProcessTime;

    /**
     * 延误天数
     */
    @ExcelProperty(value = "延误天数")
    private Integer delayDays;

    /**
     * 客户PO号
     */
    @ExcelProperty(value = "客户PO号")
    private String customerPoNumber;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 下单员
     */
    @ExcelProperty(value = "下单员")
    private String orderTaker;

    /**
     * 跟单负责人
     */
    @ExcelProperty(value = "跟单负责人")
    private String followUpResponsiblePerson;

    /**
     * 工艺编制人
     */
    @ExcelProperty(value = "工艺编制人")
    private String processCompilationPerson;

    /**
     * BOM责任人
     */
    @ExcelProperty(value = "BOM责任人")
    private String bomResponsiblePerson;

    /**
     * 项目经理
     */
    @ExcelProperty(value = "项目经理")
    private String projectManager;

    /**
     * 分组时间
     */
    @ExcelProperty(value = "分组时间")
    private Date groupingTime;

    /**
     * 利润中心
     */
    @ExcelProperty(value = "利润中心")
    private String profitCenter;

    /**
     * 零件分类
     */
    @ExcelProperty(value = "零件分类")
    private String partClassification;

    /**
     * URGENT
     */
    @ExcelProperty(value = "URGENT")
    private String urgent;

    /**
     * 订单类别
     */
    @ExcelProperty(value = "订单类别")
    private String orderCategory;

    /**
     * 类别描述
     */
    @ExcelProperty(value = "类别描述")
    private String categoryDescription;

    /**
     * 计划类型
     */
    @ExcelProperty(value = "计划类型")
    private String planType;

    /**
     * 品号
     */
    @ExcelProperty(value = "品号")
    private String itemNumber;

    /**
     * 内部订单号
     */
    @ExcelProperty(value = "内部订单号")
    private String internalOrderNumber;

    /**
     * 上层订单号
     */
    @ExcelProperty(value = "上层订单号")
    private String upperOrderNumber;

    /**
     * DR_NO
     */
    @ExcelProperty(value = "DR_NO")
    private String drNo;

    /**
     * DR_ITM
     */
    @ExcelProperty(value = "DR_ITM")
    private String drItm;

    /**
     * 动作
     */
    @ExcelProperty(value = "动作")
    private String action;

    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String productionWorkshop;
}
