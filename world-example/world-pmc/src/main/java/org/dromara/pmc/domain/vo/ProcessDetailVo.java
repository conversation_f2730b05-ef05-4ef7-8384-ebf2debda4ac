package org.dromara.pmc.domain.vo;

import lombok.Data;
import java.util.Date;

/**
 * 外发加工明细数据视图对象
 */
@Data
public class ProcessDetailVo {

    /** MO状态 */
    private String moStatus;

    /** 供应商简称 */
    private String vendorShortName;

    /** MO号 */
    private String moNo;

    /** 图号 */
    private String drawingNo;

    /** 客户代码 */
    private String customerCode;

    /** PR数量 */
    private Integer prQuantity;

    /** PO数量 */
    private Integer poQuantity;

    /** PO欠数 */
    private Integer poShortage;

    /** 外发时间 */
    private Date outsourceDate;

    /** PMC要求交期 */
    private Date pmcRequestDate;

    /** 跟单负责人 */
    private String pmcManager;

    /** 外发跟单员 */
    private String outsourceManager;

    /** 末工序时间 */
    private Date lastProcessTime;

    /** 客户要求交期 */
    private String customerDueDate;

    /** 是否供料 */
    private String materialProvided;

    /** 是否已出PO */
    private String poIssued;

    /** PO号 */
    private String poNo;

    /** PO项次 */
    private String poItem;

    /** PO审核 */
    private String poReview;

    /** PO创建日期 */
    private Date poCreateDate;

    /** 利润中心 */
    private String profitCenter;
}
