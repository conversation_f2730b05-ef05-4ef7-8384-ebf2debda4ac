package org.dromara.pmc.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.pmc.domain.ProcessStatisticsQueryDTO;
import org.dromara.pmc.domain.vo.ProcessDetailVo;
import org.dromara.pmc.domain.vo.ProcessStatisticsVo;
import org.dromara.pmc.mapper.ProcessStatisticsMapper;
import org.dromara.pmc.service.ProcessStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessStatisticsServiceImpl implements ProcessStatisticsService {

    @Autowired
    private ProcessStatisticsMapper processStatisticsMapper;

    @Override
    public List<ProcessStatisticsVo> listProcessStatistics(ProcessStatisticsQueryDTO param) {
        return processStatisticsMapper.listProcessStatistics(param);
    }

    @Override
    public List<ProcessDetailVo> listProcessDetail(ProcessStatisticsQueryDTO param) {
        return processStatisticsMapper.listProcessDetail(param);
    }

    @Override
    public List<ProcessStatisticsVo> listEntireProcessStatistics(ProcessStatisticsQueryDTO param) {
        return processStatisticsMapper.listEntireProcessStatistics(param);
    }

    @Override
    public List<ProcessDetailVo> listEntireProcessDetail(ProcessStatisticsQueryDTO param) {
        return processStatisticsMapper.listEntireProcessDetail(param);
    }
}
