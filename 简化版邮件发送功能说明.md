# 外发加工标签邮件发送功能（简化版）

## 功能说明

根据前端 `handleConfirmSendEmail` 方法的参数需求，实现了一个简洁的邮件发送功能。

## 实现内容

### 1. 后端API接口

**接口地址：** `POST /processLab/sendMail`

**请求参数：**
```json
{
  "to": "<EMAIL>",
  "subject": "邮件主题",
  "content": "邮件内容",
  "isHtml": false,
  "processLabIds": [1, 2, 3]
}
```

**参数说明：**
- `to`: 收件人邮箱（必填）
- `subject`: 邮件主题（必填）
- `content`: 邮件内容（必填）
- `isHtml`: 是否HTML格式（可选，默认false）
- `processLabIds`: 外发加工标签ID列表（可选）

### 2. 前端调用示例

```javascript
// handleConfirmSendEmail 方法示例
export const handleConfirmSendEmail = async (emailData) => {
  try {
    const response = await request.post('/processLab/sendMail', emailData)
    console.log('邮件发送成功:', response)
    return response
  } catch (error) {
    console.error('邮件发送失败:', error)
    throw error
  }
}

// 使用示例
const emailData = {
  to: '<EMAIL>',
  subject: '外发加工通知',
  content: '请查收外发加工信息',
  isHtml: false,
  processLabIds: [1, 2, 3]
}

await handleConfirmSendEmail(emailData)
```

## 核心文件

### 1. 控制器
- `OutwardProcessLabController.java` - 添加了 `sendMail` 方法

### 2. DTO类
- `MailSendDto.java` - 邮件发送参数封装

### 3. 主要功能
- 支持文本和HTML格式邮件
- 自动包含外发加工标签详细信息
- 完整的权限控制和异常处理
- 使用系统的 `MailUtils` 工具类发送邮件

## 邮件内容示例

### 文本格式
```
这是邮件的基础内容

=== 外发加工标签信息 ===

--- 外发加工标签 - ID: 1 ---
PR号: PR202509001
品号: ABC123
描述: 精密零件加工
数量: 100 PCS
厂商: 精密加工厂 (V001)
状态: 进行中
--------------------------------
```

### HTML格式
```html
<p>这是邮件的基础内容</p>
<br><br><h3>外发加工标签信息</h3>
<div style='border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px;'>
<h4>外发加工标签 - ID: 1</h4>
<p><strong>PR号:</strong> PR202509001</p>
<p><strong>品号:</strong> ABC123</p>
<p><strong>描述:</strong> 精密零件加工</p>
<p><strong>数量:</strong> 100 PCS</p>
<p><strong>厂商:</strong> 精密加工厂 (V001)</p>
<p><strong>状态:</strong> 进行中</p>
</div>
```

## 权限配置

需要为用户分配 `tianxin:processLab:mail` 权限才能使用邮件发送功能。

## 使用说明

1. 确保系统邮件配置正确
2. 为用户分配相应权限
3. 前端调用 `/processLab/sendMail` 接口
4. 传入符合 `MailSendDto` 结构的参数

这个实现专注于核心的邮件发送功能，去除了不必要的复杂性，完全符合前端 `handleConfirmSendEmail` 方法的使用需求。
