# 邮件发送功能配置示例
# 请将以下配置添加到您的 application.yml 或相应的配置文件中

# 天心系统邮件配置
tianxin:
  email:
    # 默认收件人，多个用逗号分隔
    default-recipients: "<EMAIL>,<EMAIL>"
    # 临时文件目录，用于存储邮件附件
    temp-dir: "${java.io.tmpdir}"

# 系统邮件配置（如果还没有配置的话）
mail:
  enabled: true
  host: smtp.example.com
  port: 587
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码）
  pass: your-password
  # 使用 STARTTLS安全连接
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: false
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0
