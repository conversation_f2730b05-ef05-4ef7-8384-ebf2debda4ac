// 外发加工标签邮件发送API调用示例
// 这个文件展示了如何在前端调用邮件发送接口

import request from '@/utils/request'

/**
 * 发送外发加工标签邮件
 * @param {Object} data 邮件发送参数
 * @returns {Promise} 发送结果
 */
export function sendProcessLabMail(data) {
  return request({
    url: '/processLab/sendMail',
    method: 'post',
    data: data
  })
}

// 使用示例1：发送简单文本邮件
export const sendSimpleTextMail = async () => {
  const mailData = {
    to: '<EMAIL>',
    subject: '外发加工通知',
    content: '请查收外发加工相关信息，如有疑问请及时联系。',
    isHtml: false
  }
  
  try {
    const response = await sendProcessLabMail(mailData)
    console.log('邮件发送成功:', response)
    return response
  } catch (error) {
    console.error('邮件发送失败:', error)
    throw error
  }
}

// 使用示例2：发送包含外发加工标签信息的HTML邮件
export const sendProcessLabInfoMail = async (processLabIds) => {
  const mailData = {
    to: '<EMAIL>,<EMAIL>',
    cc: '<EMAIL>',
    subject: '外发加工标签详细信息通知',
    content: `
      <h2 style="color: #333;">外发加工通知</h2>
      <p>尊敬的合作伙伴，</p>
      <p>以下是最新的外发加工标签信息，请查收并按时完成相关工作：</p>
    `,
    isHtml: true,
    processLabIds: processLabIds
  }
  
  try {
    const response = await sendProcessLabMail(mailData)
    console.log('邮件发送成功:', response)
    return response
  } catch (error) {
    console.error('邮件发送失败:', error)
    throw error
  }
}

// 使用示例3：批量发送邮件给多个厂商
export const sendBatchMails = async (vendorEmails, processLabIds) => {
  const promises = vendorEmails.map(email => {
    const mailData = {
      to: email,
      subject: '外发加工任务分配通知',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2c5aa0; border-bottom: 2px solid #2c5aa0; padding-bottom: 10px;">
            外发加工任务通知
          </h2>
          <p>尊敬的合作伙伴，</p>
          <p>您有新的外发加工任务需要处理，详细信息如下：</p>
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>注意事项：</strong></p>
            <ul>
              <li>请严格按照技术要求进行加工</li>
              <li>如有疑问请及时联系我们</li>
              <li>请按时完成并交付</li>
            </ul>
          </div>
          <p>感谢您的配合！</p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            此邮件由系统自动发送，请勿直接回复。如有问题请联系相关负责人。
          </p>
        </div>
      `,
      isHtml: true,
      processLabIds: processLabIds
    }
    
    return sendProcessLabMail(mailData)
  })
  
  try {
    const results = await Promise.allSettled(promises)
    const successful = results.filter(result => result.status === 'fulfilled').length
    const failed = results.filter(result => result.status === 'rejected').length
    
    console.log(`批量发送完成: 成功 ${successful} 个, 失败 ${failed} 个`)
    return { successful, failed, results }
  } catch (error) {
    console.error('批量发送邮件失败:', error)
    throw error
  }
}

// Vue组件中的使用示例
export const vueComponentExample = {
  data() {
    return {
      mailForm: {
        to: '',
        cc: '',
        bcc: '',
        subject: '',
        content: '',
        isHtml: false,
        processLabIds: []
      },
      mailRules: {
        to: [
          { required: true, message: '收件人不能为空', trigger: 'blur' },
          { 
            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(,[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$/, 
            message: '请输入正确的邮箱格式，多个邮箱用逗号分隔', 
            trigger: 'blur' 
          }
        ],
        subject: [
          { required: true, message: '邮件主题不能为空', trigger: 'blur' },
          { max: 100, message: '主题长度不能超过100个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '邮件内容不能为空', trigger: 'blur' },
          { max: 5000, message: '内容长度不能超过5000个字符', trigger: 'blur' }
        ]
      },
      sending: false
    }
  },
  methods: {
    async sendMail() {
      try {
        await this.$refs.mailForm.validate()
        this.sending = true
        
        await sendProcessLabMail(this.mailForm)
        this.$message.success('邮件发送成功')
        
        // 重置表单
        this.$refs.mailForm.resetFields()
        this.mailForm.processLabIds = []
      } catch (error) {
        this.$message.error('邮件发送失败：' + (error.message || '未知错误'))
      } finally {
        this.sending = false
      }
    },
    
    // 预设邮件模板
    useTemplate(templateType) {
      switch (templateType) {
        case 'urgent':
          this.mailForm.subject = '【紧急】外发加工任务通知'
          this.mailForm.content = `
            <div style="color: #d32f2f; font-weight: bold; margin-bottom: 15px;">
              ⚠️ 紧急任务通知
            </div>
            <p>尊敬的合作伙伴，</p>
            <p>您有紧急的外发加工任务需要处理，请优先安排：</p>
          `
          this.mailForm.isHtml = true
          break
          
        case 'reminder':
          this.mailForm.subject = '外发加工进度提醒'
          this.mailForm.content = `
            <p>尊敬的合作伙伴，</p>
            <p>这是关于外发加工任务的进度提醒，请查看以下信息并及时反馈进度：</p>
          `
          this.mailForm.isHtml = true
          break
          
        case 'completion':
          this.mailForm.subject = '外发加工任务完成确认'
          this.mailForm.content = `
            <p>尊敬的合作伙伴，</p>
            <p>感谢您完成外发加工任务，请确认以下信息是否正确：</p>
          `
          this.mailForm.isHtml = true
          break
          
        default:
          this.mailForm.subject = '外发加工通知'
          this.mailForm.content = '请查收外发加工相关信息。'
          this.mailForm.isHtml = false
      }
    }
  }
}

// 错误处理示例
export const handleMailError = (error) => {
  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response
    switch (status) {
      case 400:
        return '请求参数错误：' + (data.msg || '请检查邮件格式')
      case 401:
        return '未授权：请先登录'
      case 403:
        return '权限不足：您没有发送邮件的权限'
      case 500:
        return '服务器错误：' + (data.msg || '邮件发送失败')
      default:
        return '发送失败：' + (data.msg || '未知错误')
    }
  } else if (error.request) {
    // 网络错误
    return '网络错误：请检查网络连接'
  } else {
    // 其他错误
    return '发送失败：' + error.message
  }
}

// 邮件地址验证工具函数
export const validateEmails = (emails) => {
  if (!emails) return false
  
  const emailList = emails.split(/[,;]/).map(email => email.trim())
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  
  return emailList.every(email => emailRegex.test(email))
}

// 导出所有函数
export default {
  sendProcessLabMail,
  sendSimpleTextMail,
  sendProcessLabInfoMail,
  sendBatchMails,
  handleMailError,
  validateEmails,
  vueComponentExample
}
