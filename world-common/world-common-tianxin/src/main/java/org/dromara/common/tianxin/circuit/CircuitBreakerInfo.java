package org.dromara.common.tianxin.circuit;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 熔断器信息
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Builder
public class CircuitBreakerInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 熔断器名称
     */
    private String name;

    /**
     * 熔断器状态
     */
    private CircuitBreaker.State state;

    /**
     * 失败次数
     */
    private int failureCount;

    /**
     * 成功次数
     */
    private int successCount;

    /**
     * 总请求次数
     */
    private int requestCount;

    /**
     * 失败率
     */
    private double failureRate;

    /**
     * 最后失败时间
     */
    private long lastFailureTime;
}
