package org.dromara.common.tianxin.client;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tianxin.domain.request.*;
import org.dromara.common.tianxin.domain.response.ApiResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 天心天思API客户端
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Slf4j
@Component
public class TianxinApiClient {

    @Autowired
    private TxBaseClient baseClient;

    @Autowired
    private TxAuthClient authClient;

    // ==================== 基础资料管理 ====================

    /**
     * 同步商品信息
     *
     * @param request 商品请求对象
     * @return API结果
     */
    public ApiResult<Object> syncProduct(ProductRequest request) {
        return callApiWithAuth("/basic/product/sync", request);
    }

    /**
     * 查询商品信息
     *
     * @param productCode 商品编码
     * @return API结果
     */
    public ApiResult<Object> getProduct(String productCode) {
        return callApiWithAuth("/basic/product/get/" + productCode, null);
    }

    /**
     * 批量同步商品信息
     *
     * @param requests 商品请求对象列表
     * @return API结果
     */
    public ApiResult<Object> batchSyncProducts(java.util.List<ProductRequest> requests) {
        return callApiWithAuth("/basic/product/batch-sync", requests);
    }

    /**
     * 同步客户信息
     *
     * @param request 客户请求对象
     * @return API结果
     */
    public ApiResult<Object> syncCustomer(CustomerRequest request) {
        return callApiWithAuth("/basic/customer/sync", request);
    }

    /**
     * 查询客户信息
     *
     * @param customerCode 客户编码
     * @return API结果
     */
    public ApiResult<Object> getCustomer(String customerCode) {
        return callApiWithAuth("/basic/customer/get/" + customerCode, null);
    }

    /**
     * 批量同步客户信息
     *
     * @param requests 客户请求对象列表
     * @return API结果
     */
    public ApiResult<Object> batchSyncCustomers(java.util.List<CustomerRequest> requests) {
        return callApiWithAuth("/basic/customer/batch-sync", requests);
    }

    /**
     * 同步供应商信息
     *
     * @param request 供应商请求对象
     * @return API结果
     */
    public ApiResult<Object> syncSupplier(SupplierRequest request) {
        return callApiWithAuth("/basic/supplier/sync", request);
    }

    /**
     * 查询供应商信息
     *
     * @param supplierCode 供应商编码
     * @return API结果
     */
    public ApiResult<Object> getSupplier(String supplierCode) {
        return callApiWithAuth("/basic/supplier/get/" + supplierCode, null);
    }

    /**
     * 批量同步供应商信息
     *
     * @param requests 供应商请求对象列表
     * @return API结果
     */
    public ApiResult<Object> batchSyncSuppliers(java.util.List<SupplierRequest> requests) {
        return callApiWithAuth("/basic/supplier/batch-sync", requests);
    }

    // ==================== 进销存管理 ====================

    /**
     * 同步库存信息
     *
     * @param request 库存请求对象
     * @return API结果
     */
    public ApiResult<Object> syncInventory(Object request) {
        return callApiWithAuth("/inventory/sync", request);
    }

    /**
     * 查询库存信息
     *
     * @param productCode 商品编码
     * @return API结果
     */
    public ApiResult<Object> getInventory(String productCode) {
        return callApiWithAuth("/inventory/get/" + productCode, null);
    }

    /**
     * 同步销售订单
     *
     * @param request 销售订单请求对象
     * @return API结果
     */
    public ApiResult<Object> syncSalesOrder(Object request) {
        return callApiWithAuth("/sales/order/sync", request);
    }

    /**
     * 查询销售订单
     *
     * @param orderNo 订单号
     * @return API结果
     */
    public ApiResult<Object> getSalesOrder(String orderNo) {
        return callApiWithAuth("/sales/order/get/" + orderNo, null);
    }

    /**
     * 同步采购订单
     *
     * @param request 采购订单请求对象
     * @return API结果
     */
    public ApiResult<Object> syncPurchaseOrder(Object request) {
        return callApiWithAuth("/purchase/order/sync", request);
    }

    /**
     * 查询采购订单
     *
     * @param orderNo 订单号
     * @return API结果
     */
    public ApiResult<Object> getPurchaseOrder(String orderNo) {
        return callApiWithAuth("/purchase/order/get/" + orderNo, null);
    }

    // ==================== 生产制造 ====================

    /**
     * 同步生产计划
     *
     * @param request 生产计划请求对象
     * @return API结果
     */
    public ApiResult<Object> syncProductionPlan(Object request) {
        return callApiWithAuth("/production/plan/sync", request);
    }

    /**
     * 查询生产计划
     *
     * @param planNo 计划号
     * @return API结果
     */
    public ApiResult<Object> getProductionPlan(String planNo) {
        return callApiWithAuth("/production/plan/get/" + planNo, null);
    }

    /**
     * 同步生产订单
     *
     * @param request 生产订单请求对象
     * @return API结果
     */
    public ApiResult<Object> syncProductionOrder(Object request) {
        return callApiWithAuth("/production/order/sync", request);
    }

    /**
     * 查询生产订单
     *
     * @param orderNo 订单号
     * @return API结果
     */
    public ApiResult<Object> getProductionOrder(String orderNo) {
        return callApiWithAuth("/production/order/get/" + orderNo, null);
    }

    // ==================== 财务管理 ====================

    /**
     * 同步应收应付
     *
     * @param request 应收应付请求对象
     * @return API结果
     */
    public ApiResult<Object> syncReceivablePayable(Object request) {
        return callApiWithAuth("/finance/receivable-payable/sync", request);
    }

    /**
     * 查询应收应付
     *
     * @param billNo 单据号
     * @return API结果
     */
    public ApiResult<Object> getReceivablePayable(String billNo) {
        return callApiWithAuth("/finance/receivable-payable/get/" + billNo, null);
    }

    // ==================== 通用方法 ====================

    /**
     * 带认证的API调用
     *
     * @param endpoint 接口端点
     * @param request  请求对象
     * @return API结果
     */
    private ApiResult<Object> callApiWithAuth(String endpoint, Object request) {
        try {
            // 获取访问令牌
            String accessToken = authClient.getAccessToken();

            // 调用API（带认证）
            if (request != null) {
                return baseClient.post(endpoint, request, accessToken);
            } else {
                return baseClient.get(endpoint, accessToken);
            }
        } catch (Exception e) {
            log.error("天心API调用异常: {}", endpoint, e);
            return ApiResult.error("API_CALL_ERROR", "API调用异常: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     *
     * @return API结果
     */
    public ApiResult<Object> healthCheck() {
        return callApiWithAuth("/health", null);
    }

    /**
     * 获取API版本信息
     *
     * @return API结果
     */
    public ApiResult<Object> getVersion() {
        return callApiWithAuth("/version", null);
    }
}
