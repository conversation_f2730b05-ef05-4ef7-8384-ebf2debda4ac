package org.dromara.common.tianxin.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 客户信息请求对象
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerRequest extends BaseRequest {

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户简称
     */
    private String shortName;

    /**
     * 客户类型：1-企业，2-个人
     */
    private Integer customerType;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 客户地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 客户等级：1-A级，2-B级，3-C级
     */
    private Integer customerLevel;

    /**
     * 信用额度
     */
    private String creditLimit;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 客户状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;
}
