package org.dromara.common.tianxin.domain.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 天心天思API调用结果
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
public class ApiResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 创建成功结果
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return API结果
     */
    public static <T> ApiResult<T> success(T data) {
        ApiResult<T> result = new ApiResult<>();
        result.setSuccess(true);
        result.setData(data);
        return result;
    }

    /**
     * 创建成功结果
     *
     * @param <T> 数据类型
     * @return API结果
     */
    public static <T> ApiResult<T> success() {
        return success(null);
    }

    /**
     * 创建失败结果
     *
     * @param errorCode    错误码
     * @param errorMessage 错误消息
     * @param <T>          数据类型
     * @return API结果
     */
    public static <T> ApiResult<T> error(String errorCode, String errorMessage) {
        ApiResult<T> result = new ApiResult<>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param errorMessage 错误消息
     * @param <T>          数据类型
     * @return API结果
     */
    public static <T> ApiResult<T> error(String errorMessage) {
        return error("API_ERROR", errorMessage);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isFailed() {
        return !isSuccess();
    }
}
