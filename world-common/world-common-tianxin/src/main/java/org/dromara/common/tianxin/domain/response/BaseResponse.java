package org.dromara.common.tianxin.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 天心天思API基础响应对象
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class BaseResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private Object data;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime responseTime;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 是否成功
     */
    private Boolean success;

    public BaseResponse() {
        this.responseTime = LocalDateTime.now();
    }

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @return 响应对象
     */
    public static BaseResponse success(Object data) {
        BaseResponse response = new BaseResponse();
        response.setCode(200);
        response.setMessage("操作成功");
        response.setData(data);
        response.setSuccess(true);
        return response;
    }

    /**
     * 成功响应
     *
     * @param message 响应消息
     * @param data    响应数据
     * @return 响应对象
     */
    public static BaseResponse success(String message, Object data) {
        BaseResponse response = new BaseResponse();
        response.setCode(200);
        response.setMessage(message);
        response.setData(data);
        response.setSuccess(true);
        return response;
    }

    /**
     * 失败响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @return 响应对象
     */
    public static BaseResponse error(Integer code, String message) {
        BaseResponse response = new BaseResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setSuccess(false);
        return response;
    }

    /**
     * 失败响应
     *
     * @param message 错误消息
     * @return 响应对象
     */
    public static BaseResponse error(String message) {
        return error(500, message);
    }
}
