package org.dromara.common.tianxin.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.PostConstruct;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tianxin.circuit.CircuitBreaker;
import org.dromara.common.tianxin.circuit.CircuitBreakerInfo;
import org.dromara.common.tianxin.config.TianxinProperties;
import org.dromara.common.tianxin.domain.response.ApiResult;
import org.dromara.common.tianxin.exception.CircuitBreakerOpenException;
import org.dromara.common.tianxin.exception.TianxinApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 天心天思API基础客户端
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Slf4j
@Component
public class TxBaseClient {

    @Autowired
    private TianxinProperties properties;

    @Autowired(required = false)
    private Validator validator;

    private CircuitBreaker circuitBreaker;

    @PostConstruct
    public void init() {
        // 初始化熔断器
        if (properties.getCircuitBreakerEnabled()) {
            circuitBreaker = new CircuitBreaker(
                "TianxinAPI",
                properties.getCircuitBreakerFailureThreshold(),
                properties.getCircuitBreakerTimeout(),
                properties.getCircuitBreakerHalfOpenMaxCalls()
            );
            log.info("天心API熔断器初始化完成: failureThreshold={}, timeout={}ms",
                properties.getCircuitBreakerFailureThreshold(), properties.getCircuitBreakerTimeout());
        }
    }

    /**
     * 发送POST请求
     *
     * @param endpoint 接口端点
     * @param request  请求对象
     * @param <T>      请求类型
     * @param <R>      响应类型
     * @return API结果
     */
    public <T, R> ApiResult<R> post(String endpoint, T request) {
        // 验证请求参数
        if (request != null) {
            validateRequest(request);
        }
        return executeRequest("POST", endpoint, request, null);
    }

    /**
     * 发送POST请求（带认证）
     *
     * @param endpoint  接口端点
     * @param request   请求对象
     * @param authToken 认证令牌
     * @param <T>       请求类型
     * @param <R>       响应类型
     * @return API结果
     */
    public <T, R> ApiResult<R> post(String endpoint, T request, String authToken) {
        // 验证请求参数
        if (request != null) {
            validateRequest(request);
        }
        return executeRequest("POST", endpoint, request, authToken);
    }

    /**
     * 发送GET请求
     *
     * @param endpoint 接口端点
     * @param <R>      响应类型
     * @return API结果
     */
    public <R> ApiResult<R> get(String endpoint) {
        return executeRequest("GET", endpoint, null, null);
    }

    /**
     * 发送GET请求（带认证）
     *
     * @param endpoint  接口端点
     * @param authToken 认证令牌
     * @param <R>       响应类型
     * @return API结果
     */
    public <R> ApiResult<R> get(String endpoint, String authToken) {
        return executeRequest("GET", endpoint, null, authToken);
    }

    /**
     * 发送PUT请求
     *
     * @param endpoint 接口端点
     * @param request  请求对象
     * @param <T>      请求类型
     * @param <R>      响应类型
     * @return API结果
     */
    public <T, R> ApiResult<R> put(String endpoint, T request) {
        return executeRequest("PUT", endpoint, request, null);
    }

    /**
     * 发送DELETE请求
     *
     * @param endpoint 接口端点
     * @param <R>      响应类型
     * @return API结果
     */
    public <R> ApiResult<R> delete(String endpoint) {
        return executeRequest("DELETE", endpoint, null, null);
    }

    /**
     * 执行HTTP请求（带重试机制）
     *
     * @param method    请求方法
     * @param endpoint  接口端点
     * @param request   请求对象
     * @param authToken 认证令牌
     * @param <T>       请求类型
     * @param <R>       响应类型
     * @return API结果
     */
    private <T, R> ApiResult<R> executeRequest(String method, String endpoint, T request, String authToken) {
        try {
            // 使用熔断器执行请求
            if (circuitBreaker != null) {
                return circuitBreaker.execute(() -> executeRequestWithRetry(method, endpoint, request, authToken, 0));
            } else {
                return executeRequestWithRetry(method, endpoint, request, authToken, 0);
            }
        } catch (CircuitBreakerOpenException e) {
            log.warn("熔断器开启，拒绝请求: {}", endpoint);
            return ApiResult.error("CIRCUIT_BREAKER_OPEN", "熔断器开启，服务暂时不可用");
        } catch (Exception e) {
            log.error("天心API请求执行异常: {}", endpoint, e);
            return ApiResult.error("EXECUTION_ERROR", "请求执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行HTTP请求（带重试机制）
     *
     * @param method     请求方法
     * @param endpoint   接口端点
     * @param request    请求对象
     * @param authToken  认证令牌
     * @param retryCount 当前重试次数
     * @param <T>        请求类型
     * @param <R>        响应类型
     * @return API结果
     */
    private <T, R> ApiResult<R> executeRequestWithRetry(String method, String endpoint, T request, String authToken, int retryCount) {
        long startTime = System.currentTimeMillis();
        String requestId = generateRequestId();

        try {
            // 构建请求URL
            String url = properties.getBaseUrl() + endpoint;

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-Request-ID", requestId);
            headers.put("User-Agent", "World-MES-Client/1.0");

            // 添加认证头
            if (StrUtil.isNotBlank(authToken)) {
                headers.put("Authorization", "Bearer " + authToken);
            }

            // 构建请求体
            String requestBody = null;
            if (request != null) {
                requestBody = JSONUtil.toJsonStr(request);
            }

            // 记录请求日志
            if (properties.getLogEnabled()) {
                log.info("发送天心API请求: {} {}, RequestId: {}, RetryCount: {}, Body: {}",
                    method, url, requestId, retryCount, requestBody);
            }

            // 执行请求
            HttpResponse response = executeHttpRequest(method, url, headers, requestBody);

            long responseTime = System.currentTimeMillis() - startTime;

            // 检查响应状态
            if (!response.isOk()) {
                String errorMessage = "HTTP请求失败: " + response.getStatus() + " " + response.body();
                log.error("天心API请求失败: {} {}, Response: {}", method, url, errorMessage);

                // 判断是否需要重试
                if (shouldRetry(response.getStatus(), retryCount)) {
                    return handleRetry(method, endpoint, request, authToken, retryCount, errorMessage);
                }

                return ApiResult.error("HTTP_ERROR", errorMessage);
            }

            String responseBody = response.body();

            // 记录响应日志
            if (properties.getLogEnabled()) {
                log.info("天心API响应: {} {}, ResponseTime: {}ms, RequestId: {}, Body: {}",
                    method, url, responseTime, requestId, responseBody);
            }

            // 解析响应
            ApiResult<R> result = JSONUtil.toBean(responseBody, ApiResult.class);
            result.setRequestId(requestId);
            result.setResponseTime(responseTime);

            return result;

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            log.error("天心API请求异常: {} {}, ResponseTime: {}ms, RequestId: {}, RetryCount: {}",
                method, endpoint, responseTime, requestId, retryCount, e);

            // 判断是否需要重试
            if (shouldRetryForException(e, retryCount)) {
                return handleRetry(method, endpoint, request, authToken, retryCount, e.getMessage());
            }

            return ApiResult.error("SYSTEM_ERROR", "系统异常: " + e.getMessage());
        }
    }

    /**
     * 判断是否应该重试（基于HTTP状态码）
     *
     * @param statusCode HTTP状态码
     * @param retryCount 当前重试次数
     * @return 是否应该重试
     */
    private boolean shouldRetry(int statusCode, int retryCount) {
        if (retryCount >= properties.getRetryTimes()) {
            return false;
        }

        // 5xx服务器错误和429限流错误可以重试
        return statusCode >= 500 || statusCode == 429;
    }

    /**
     * 判断是否应该重试（基于异常类型）
     *
     * @param exception  异常
     * @param retryCount 当前重试次数
     * @return 是否应该重试
     */
    private boolean shouldRetryForException(Exception exception, int retryCount) {
        if (retryCount >= properties.getRetryTimes()) {
            return false;
        }

        // 网络超时、连接异常等可以重试
        String message = exception.getMessage();
        return message != null && (
            message.contains("timeout") ||
                message.contains("connection") ||
                message.contains("network") ||
                message.contains("SocketTimeoutException") ||
                message.contains("ConnectException")
        );
    }

    /**
     * 处理重试逻辑
     *
     * @param method       请求方法
     * @param endpoint     接口端点
     * @param request      请求对象
     * @param authToken    认证令牌
     * @param retryCount   当前重试次数
     * @param errorMessage 错误消息
     * @param <T>          请求类型
     * @param <R>          响应类型
     * @return API结果
     */
    private <T, R> ApiResult<R> handleRetry(String method, String endpoint, T request, String authToken, int retryCount, String errorMessage) {
        int nextRetryCount = retryCount + 1;
        long retryDelay = calculateRetryDelay(nextRetryCount);

        log.warn("天心API请求失败，准备重试: {} {}, RetryCount: {}/{}, RetryDelay: {}ms, Error: {}",
            method, endpoint, nextRetryCount, properties.getRetryTimes(), retryDelay, errorMessage);

        try {
            Thread.sleep(retryDelay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("重试等待被中断", e);
            return ApiResult.error("RETRY_INTERRUPTED", "重试被中断");
        }

        return executeRequestWithRetry(method, endpoint, request, authToken, nextRetryCount);
    }

    /**
     * 计算重试延迟时间（指数退避）
     *
     * @param retryCount 重试次数
     * @return 延迟时间（毫秒）
     */
    private long calculateRetryDelay(int retryCount) {
        // 指数退避：基础延迟 * 2^(重试次数-1)
        long baseDelay = properties.getRetryInterval();
        long delay = baseDelay * (1L << (retryCount - 1));

        // 最大延迟时间限制为30秒
        return Math.min(delay, 30000L);
    }

    /**
     * 执行HTTP请求
     *
     * @param method      请求方法
     * @param url         请求URL
     * @param headers     请求头
     * @param requestBody 请求体
     * @return HTTP响应
     */
    private HttpResponse executeHttpRequest(String method, String url, Map<String, String> headers, String requestBody) {
        HttpRequest request;

        switch (method.toUpperCase()) {
            case "POST":
                request = HttpRequest.post(url);
                break;
            case "PUT":
                request = HttpRequest.put(url);
                break;
            case "DELETE":
                request = HttpRequest.delete(url);
                break;
            case "GET":
            default:
                request = HttpRequest.get(url);
                break;
        }

        // 设置请求头
        headers.forEach(request::header);

        // 设置请求体
        if (StrUtil.isNotBlank(requestBody)) {
            request.body(requestBody);
        }

        // 设置超时时间
        request.timeout(properties.getTimeout());

        // 执行请求
        return request.execute();
    }

    /**
     * 生成请求ID
     *
     * @return 请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 获取熔断器信息
     *
     * @return 熔断器信息
     */
    public CircuitBreakerInfo getCircuitBreakerInfo() {
        if (circuitBreaker == null) {
            return null;
        }
        return circuitBreaker.getInfo();
    }

    /**
     * 重置熔断器
     */
    public void resetCircuitBreaker() {
        if (circuitBreaker != null) {
            circuitBreaker.reset();
        }
    }

    /**
     * 获取健康状态
     *
     * @return 健康状态
     */
    public boolean isHealthy() {
        if (circuitBreaker == null) {
            return true;
        }
        return circuitBreaker.getState() != CircuitBreaker.State.OPEN;
    }

    /**
     * 验证请求参数
     *
     * @param request 请求对象
     * @param <T>     请求类型
     */
    private <T> void validateRequest(T request) {
        if (validator == null || request == null) {
            return;
        }

        Set<ConstraintViolation<T>> violations = validator.validate(request);
        if (!violations.isEmpty()) {
            StringBuilder errorMessage = new StringBuilder("请求参数验证失败: ");
            for (ConstraintViolation<T> violation : violations) {
                errorMessage.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
            }
            throw new TianxinApiException("VALIDATION_ERROR", errorMessage.toString());
        }
    }
}
