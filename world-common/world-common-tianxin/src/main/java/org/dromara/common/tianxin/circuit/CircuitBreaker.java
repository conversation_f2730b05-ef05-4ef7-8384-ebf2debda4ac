package org.dromara.common.tianxin.circuit;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tianxin.exception.CircuitBreakerOpenException;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 熔断器实现
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Slf4j
public class CircuitBreaker {

    /**
     * 熔断器状态
     */
    public enum State {
        CLOSED,    // 关闭状态，正常请求
        OPEN,      // 开启状态，拒绝请求
        HALF_OPEN  // 半开状态，允许部分请求通过
    }

    private final String name;
    private final int failureThreshold;        // 失败阈值
    private final long timeout;                // 熔断超时时间（毫秒）
    private final int halfOpenMaxCalls;        // 半开状态最大请求数

    private final AtomicReference<State> state = new AtomicReference<>(State.CLOSED);
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger requestCount = new AtomicInteger(0);
    private final AtomicLong lastFailureTime = new AtomicLong(0);
    private final AtomicInteger halfOpenCalls = new AtomicInteger(0);

    public CircuitBreaker(String name, int failureThreshold, long timeout, int halfOpenMaxCalls) {
        this.name = name;
        this.failureThreshold = failureThreshold;
        this.timeout = timeout;
        this.halfOpenMaxCalls = halfOpenMaxCalls;
    }

    /**
     * 执行请求
     *
     * @param supplier 请求执行器
     * @param <T> 返回类型
     * @return 执行结果
     * @throws CircuitBreakerOpenException 熔断器开启异常
     */
    public <T> T execute(CircuitBreakerSupplier<T> supplier) throws Exception {
        if (!allowRequest()) {
            throw new CircuitBreakerOpenException("熔断器开启，拒绝请求: " + name);
        }

        try {
            T result = supplier.get();
            onSuccess();
            return result;
        } catch (Exception e) {
            onFailure();
            throw e;
        }
    }

    /**
     * 判断是否允许请求
     *
     * @return 是否允许请求
     */
    private boolean allowRequest() {
        State currentState = state.get();

        switch (currentState) {
            case CLOSED:
                return true;
            case OPEN:
                if (shouldAttemptReset()) {
                    state.compareAndSet(State.OPEN, State.HALF_OPEN);
                    halfOpenCalls.set(0);
                    log.info("熔断器 {} 从开启状态转为半开状态", name);
                    return true;
                }
                return false;
            case HALF_OPEN:
                int currentHalfOpenCalls = halfOpenCalls.incrementAndGet();
                return currentHalfOpenCalls <= halfOpenMaxCalls;
            default:
                return false;
        }
    }

    /**
     * 判断是否应该尝试重置熔断器
     *
     * @return 是否应该重置
     */
    private boolean shouldAttemptReset() {
        long currentTime = System.currentTimeMillis();
        long lastFailure = lastFailureTime.get();
        return currentTime - lastFailure >= timeout;
    }

    /**
     * 请求成功处理
     */
    private void onSuccess() {
        successCount.incrementAndGet();
        requestCount.incrementAndGet();

        State currentState = state.get();
        if (currentState == State.HALF_OPEN) {
            // 半开状态下，如果成功请求达到阈值，则关闭熔断器
            if (halfOpenCalls.get() >= halfOpenMaxCalls) {
                state.compareAndSet(State.HALF_OPEN, State.CLOSED);
                resetCounters();
                log.info("熔断器 {} 从半开状态转为关闭状态", name);
            }
        } else if (currentState == State.CLOSED) {
            // 关闭状态下，如果连续成功，重置失败计数
            if (successCount.get() >= 5) {
                failureCount.set(0);
            }
        }
    }

    /**
     * 请求失败处理
     */
    private void onFailure() {
        failureCount.incrementAndGet();
        requestCount.incrementAndGet();
        lastFailureTime.set(System.currentTimeMillis());

        State currentState = state.get();
        if (currentState == State.CLOSED) {
            // 关闭状态下，检查是否达到失败阈值
            if (failureCount.get() >= failureThreshold) {
                state.compareAndSet(State.CLOSED, State.OPEN);
                log.warn("熔断器 {} 从关闭状态转为开启状态，失败次数: {}", name, failureCount.get());
            }
        } else if (currentState == State.HALF_OPEN) {
            // 半开状态下，如果有失败，立即开启熔断器
            state.compareAndSet(State.HALF_OPEN, State.OPEN);
            log.warn("熔断器 {} 从半开状态转为开启状态", name);
        }
    }

    /**
     * 重置计数器
     */
    private void resetCounters() {
        failureCount.set(0);
        successCount.set(0);
        requestCount.set(0);
        halfOpenCalls.set(0);
    }

    /**
     * 获取熔断器状态
     *
     * @return 熔断器状态
     */
    public State getState() {
        return state.get();
    }

    /**
     * 获取失败次数
     *
     * @return 失败次数
     */
    public int getFailureCount() {
        return failureCount.get();
    }

    /**
     * 获取成功次数
     *
     * @return 成功次数
     */
    public int getSuccessCount() {
        return successCount.get();
    }

    /**
     * 获取总请求次数
     *
     * @return 总请求次数
     */
    public int getRequestCount() {
        return requestCount.get();
    }

    /**
     * 获取失败率
     *
     * @return 失败率
     */
    public double getFailureRate() {
        int total = requestCount.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) failureCount.get() / total;
    }

    /**
     * 获取熔断器信息
     *
     * @return 熔断器信息
     */
    public CircuitBreakerInfo getInfo() {
        return CircuitBreakerInfo.builder()
                .name(name)
                .state(state.get())
                .failureCount(failureCount.get())
                .successCount(successCount.get())
                .requestCount(requestCount.get())
                .failureRate(getFailureRate())
                .lastFailureTime(lastFailureTime.get())
                .build();
    }

    /**
     * 手动重置熔断器
     */
    public void reset() {
        state.set(State.CLOSED);
        resetCounters();
        log.info("熔断器 {} 已手动重置", name);
    }

    /**
     * 熔断器供应商接口
     *
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface CircuitBreakerSupplier<T> {
        T get() throws Exception;
    }
}
