package org.dromara.common.tianxin.client;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tianxin.config.TianxinProperties;
import org.dromara.common.tianxin.exception.TianxinAuthException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * 天心天思API认证客户端
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Slf4j
@Component
public class TxAuthClient {

    @Autowired
    private TianxinProperties properties;

    private static final String TOKEN_CACHE_KEY = "tianxin:access_token";
    private static final String TOKEN_EXPIRE_KEY = "tianxin:token_expire";

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    public String getAccessToken() {
        // 检查缓存中的令牌
        String cachedToken = RedisUtils.getCacheObject(TOKEN_CACHE_KEY);
        if (StrUtil.isNotBlank(cachedToken) && !isTokenExpired()) {
            return cachedToken;
        }

        // 获取新令牌
        return refreshToken();
    }

    /**
     * 刷新访问令牌
     *
     * @return 新的访问令牌
     */
    public String refreshToken() {
        try {
            // 构建认证请求
            Map<String, Object> authRequest = new HashMap<>();
            authRequest.put("appId", properties.getAppId());
            authRequest.put("appSecret", properties.getAppSecret());
            authRequest.put("grantType", "client_credentials");

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("User-Agent", "World-MES-Client/1.0");

            // 构建请求URL
            String url = properties.getBaseUrl() + "/auth/token";

            // 记录请求日志
            if (properties.getLogEnabled()) {
                log.info("发送天心API认证请求: {}", url);
            }

            // 执行认证请求
            HttpRequest request = HttpRequest.post(url);
            headers.forEach(request::header);
            request.body(JSONUtil.toJsonStr(authRequest));
            request.timeout(properties.getTimeout());

            HttpResponse response = request.execute();

            if (!response.isOk()) {
                String errorMessage = "认证请求失败: " + response.getStatus() + " " + response.body();
                log.error("天心API认证失败: {}", errorMessage);
                throw new TianxinAuthException(errorMessage);
            }

            String responseBody = response.body();

            // 记录响应日志
            if (properties.getLogEnabled()) {
                log.info("天心API认证响应: {}", responseBody);
            }

            // 解析响应
            Map<String, Object> authResponse = JSONUtil.toBean(responseBody, Map.class);

            if (authResponse == null || !authResponse.containsKey("accessToken")) {
                String errorMessage = "认证响应格式错误: " + responseBody;
                log.error("天心API认证响应解析失败: {}", errorMessage);
                throw new TianxinAuthException(errorMessage);
            }

            String accessToken = (String) authResponse.get("accessToken");
            Object expiresInObj = authResponse.get("expiresIn");
            long expiresIn = expiresInObj != null ? Long.parseLong(expiresInObj.toString()) : 3600L;

            // 缓存令牌
            RedisUtils.setCacheObject(TOKEN_CACHE_KEY, accessToken, Duration.ofSeconds(expiresIn));

            // 缓存过期时间
            LocalDateTime expireTime = LocalDateTime.now().plusSeconds(expiresIn);
            RedisUtils.setCacheObject(TOKEN_EXPIRE_KEY, expireTime.toEpochSecond(ZoneOffset.UTC), Duration.ofSeconds(expiresIn));

            log.info("天心API访问令牌刷新成功，过期时间: {}", expireTime);
            return accessToken;

        } catch (Exception e) {
            log.error("天心API访问令牌刷新异常", e);
            throw new TianxinAuthException("访问令牌刷新异常: " + e.getMessage(), e);
        }
    }

    /**
     * 检查令牌是否过期
     *
     * @return 是否过期
     */
    private boolean isTokenExpired() {
        try {
            Long expireTimestamp = RedisUtils.getCacheObject(TOKEN_EXPIRE_KEY);
            if (expireTimestamp == null) {
                return true;
            }

            LocalDateTime expireTime = LocalDateTime.ofEpochSecond(expireTimestamp, 0, ZoneOffset.UTC);
            return LocalDateTime.now().isAfter(expireTime.minusMinutes(5)); // 提前5分钟过期
        } catch (Exception e) {
            log.warn("检查令牌过期时间异常", e);
            return true;
        }
    }

    /**
     * 清除访问令牌缓存
     */
    public void clearTokenCache() {
        RedisUtils.deleteObject(TOKEN_CACHE_KEY);
        RedisUtils.deleteObject(TOKEN_EXPIRE_KEY);
        log.info("天心API访问令牌缓存已清除");
    }

    /**
     * 验证令牌有效性
     *
     * @param token 访问令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            if (StrUtil.isBlank(token)) {
                return false;
            }

            // 构建验证请求
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);
            headers.put("User-Agent", "World-MES-Client/1.0");

            // 构建请求URL
            String url = properties.getBaseUrl() + "/auth/validate";

            // 执行验证请求
            HttpRequest request = HttpRequest.get(url);
            headers.forEach(request::header);
            request.timeout(properties.getTimeout());

            HttpResponse response = request.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                Map<String, Object> result = JSONUtil.toBean(responseBody, Map.class);
                return result != null && Boolean.TRUE.equals(result.get("valid"));
            }

            return false;
        } catch (Exception e) {
            log.warn("验证令牌有效性异常", e);
            return false;
        }
    }

    /**
     * 获取令牌信息
     *
     * @return 令牌信息
     */
    public Map<String, Object> getTokenInfo() {
        try {
            String token = getAccessToken();
            if (StrUtil.isBlank(token)) {
                return null;
            }

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);
            headers.put("User-Agent", "World-MES-Client/1.0");

            // 构建请求URL
            String url = properties.getBaseUrl() + "/auth/info";

            // 执行请求
            HttpRequest request = HttpRequest.get(url);
            headers.forEach(request::header);
            request.timeout(properties.getTimeout());

            HttpResponse response = request.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                return JSONUtil.toBean(responseBody, Map.class);
            }

            return null;
        } catch (Exception e) {
            log.warn("获取令牌信息异常", e);
            return null;
        }
    }
}
