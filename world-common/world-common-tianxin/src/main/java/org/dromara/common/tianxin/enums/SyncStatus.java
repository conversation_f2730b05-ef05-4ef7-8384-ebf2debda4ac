package org.dromara.common.tianxin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 同步状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum SyncStatus {

    /**
     * 待同步
     */
    PENDING(0, "待同步"),

    /**
     * 同步中
     */
    SYNCING(1, "同步中"),

    /**
     * 同步成功
     */
    SUCCESS(2, "同步成功"),

    /**
     * 同步失败
     */
    FAILED(3, "同步失败"),

    /**
     * 已取消
     */
    CANCELLED(4, "已取消");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 同步状态枚举
     */
    public static SyncStatus getByCode(Integer code) {
        for (SyncStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的同步状态码: " + code);
    }
}
