package org.dromara.common.tianxin.config;

import org.dromara.common.tianxin.client.TianxinApiClient;
import org.dromara.common.tianxin.client.TxAuthClient;
import org.dromara.common.tianxin.client.TxBaseClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * 天心API自动配置类
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "tianxin.api", name = "enabled", havingValue = "true", matchIfMissing = true)
@ComponentScan(basePackages = "org.dromara.common.tianxin")
public class TianxinAutoConfiguration {

    /**
     * 天心API基础客户端
     */
    @Bean
    public TxBaseClient txBaseClient() {
        return new TxBaseClient();
    }

    /**
     * 天心API认证客户端
     */
    @Bean
    public TxAuthClient txAuthClient() {
        return new TxAuthClient();
    }

    /**
     * 天心API客户端
     */
    @Bean
    public TianxinApiClient tianxinApiClient() {
        return new TianxinApiClient();
    }
}
