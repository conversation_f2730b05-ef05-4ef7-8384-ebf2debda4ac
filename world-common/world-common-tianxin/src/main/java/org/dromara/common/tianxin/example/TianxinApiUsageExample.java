package org.dromara.common.tianxin.example;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tianxin.circuit.CircuitBreakerInfo;
import org.dromara.common.tianxin.client.TianxinApiClient;
import org.dromara.common.tianxin.client.TxBaseClient;
import org.dromara.common.tianxin.domain.request.ProductRequest;
import org.dromara.common.tianxin.domain.response.ApiResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 天心API使用示例
 * <p>
 * 本类展示了如何在不同场景下使用天心API调用工具
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Slf4j
@Component
public class TianxinApiUsageExample {

    @Autowired
    private TianxinApiClient tianxinApiClient;

    @Autowired
    private TxBaseClient txBaseClient;

    /**
     * 示例1：基础API调用
     */
    public void basicApiCall() {
        log.info("=== 示例1：基础API调用 ===");

        try {
            // 构建请求对象
            ProductRequest request = new ProductRequest();
            request.setProductCode("P001");
            request.setProductName("测试商品");
            request.setSpecification("规格型号");
            request.setUnit("个");
            request.setCategory("电子产品");
            request.setStandardPrice(BigDecimal.valueOf(100.00));
            request.setStatus(1);
            request.setOperationType("ADD");

            // 调用API
            ApiResult<Object> result = tianxinApiClient.syncProduct(request);

            if (result.isSuccess()) {
                log.info("商品同步成功: {}", result.getData());
            } else {
                log.error("商品同步失败: {}", result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("API调用异常", e);
        }
    }

    /**
     * 示例2：批量API调用
     */
    public void batchApiCall() {
        log.info("=== 示例2：批量API调用 ===");

        try {
            // 构建批量请求对象
            List<ProductRequest> requests = new ArrayList<>();

            for (int i = 1; i <= 5; i++) {
                ProductRequest request = new ProductRequest();
                request.setProductCode("P00" + i);
                request.setProductName("测试商品" + i);
                request.setSpecification("规格型号" + i);
                request.setUnit("个");
                request.setCategory("电子产品");
                request.setStandardPrice(BigDecimal.valueOf(100.00 + i * 10));
                request.setStatus(1);
                request.setOperationType("ADD");
                requests.add(request);
            }

            // 批量调用API
            ApiResult<Object> result = tianxinApiClient.batchSyncProducts(requests);

            if (result.isSuccess()) {
                log.info("批量商品同步成功: {}", result.getData());
            } else {
                log.error("批量商品同步失败: {}", result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("批量API调用异常", e);
        }
    }

    /**
     * 示例3：查询API调用
     */
    public void queryApiCall() {
        log.info("=== 示例3：查询API调用 ===");

        try {
            // 查询商品信息
            ApiResult<Object> result = tianxinApiClient.getProduct("P001");

            if (result.isSuccess()) {
                log.info("查询商品成功: {}", result.getData());
            } else {
                log.error("查询商品失败: {}", result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("查询API调用异常", e);
        }
    }

    /**
     * 示例4：错误处理
     */
    public void errorHandlingExample() {
        log.info("=== 示例4：错误处理 ===");

        try {
            // 故意使用错误的商品编码进行测试
            ApiResult<Object> result = tianxinApiClient.getProduct("INVALID_CODE");

            if (result.isSuccess()) {
                log.info("查询成功: {}", result.getData());
            } else {
                // 处理不同类型的错误
                String errorCode = result.getErrorCode();
                String errorMessage = result.getErrorMessage();

                switch (errorCode) {
                    case "CIRCUIT_BREAKER_OPEN":
                        log.warn("熔断器开启，服务暂时不可用");
                        break;
                    case "VALIDATION_ERROR":
                        log.warn("参数验证失败: {}", errorMessage);
                        break;
                    case "HTTP_ERROR":
                        log.warn("HTTP请求失败: {}", errorMessage);
                        break;
                    default:
                        log.error("未知错误: {} - {}", errorCode, errorMessage);
                }
            }
        } catch (Exception e) {
            log.error("异常处理示例", e);
        }
    }

    /**
     * 示例5：健康检查和监控
     */
    public void healthCheckExample() {
        log.info("=== 示例5：健康检查和监控 ===");

        try {
            // 检查API健康状态
            boolean isHealthy = txBaseClient.isHealthy();
            log.info("API健康状态: {}", isHealthy ? "健康" : "不健康");

            // 获取熔断器信息
            CircuitBreakerInfo circuitBreakerInfo = txBaseClient.getCircuitBreakerInfo();
            if (circuitBreakerInfo != null) {
                log.info("熔断器状态: {}", circuitBreakerInfo.getState());
                log.info("失败次数: {}", circuitBreakerInfo.getFailureCount());
                log.info("成功次数: {}", circuitBreakerInfo.getSuccessCount());
                log.info("总请求次数: {}", circuitBreakerInfo.getRequestCount());
                log.info("失败率: {:.2f}%", circuitBreakerInfo.getFailureRate() * 100);
            } else {
                log.info("熔断器未启用");
            }

            // 健康检查API调用
            ApiResult<Object> healthResult = tianxinApiClient.healthCheck();
            if (healthResult.isSuccess()) {
                log.info("健康检查API调用成功: {}", healthResult.getData());
            } else {
                log.warn("健康检查API调用失败: {}", healthResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("健康检查异常", e);
        }
    }

    /**
     * 示例6：熔断器管理
     */
    public void circuitBreakerManagement() {
        log.info("=== 示例6：熔断器管理 ===");

        try {
            // 获取熔断器信息
            CircuitBreakerInfo info = txBaseClient.getCircuitBreakerInfo();
            if (info != null) {
                log.info("当前熔断器状态: {}", info.getState());

                // 如果熔断器开启，可以手动重置
                if (info.getState().toString().equals("OPEN")) {
                    log.info("熔断器已开启，尝试重置...");
                    txBaseClient.resetCircuitBreaker();
                    log.info("熔断器重置完成");
                }
            }
        } catch (Exception e) {
            log.error("熔断器管理异常", e);
        }
    }

    /**
     * 示例7：分块批量处理
     */
    public void chunkedBatchProcessing() {
        log.info("=== 示例7：分块批量处理 ===");

        try {
            // 模拟大量数据
            List<ProductRequest> allRequests = new ArrayList<>();
            for (int i = 1; i <= 100; i++) {
                ProductRequest request = new ProductRequest();
                request.setProductCode("P" + String.format("%03d", i));
                request.setProductName("测试商品" + i);
                request.setSpecification("规格型号" + i);
                request.setUnit("个");
                request.setCategory("电子产品");
                request.setStandardPrice(BigDecimal.valueOf(100.00 + i));
                request.setStatus(1);
                request.setOperationType("ADD");
                allRequests.add(request);
            }

            // 分块处理，每批20个
            int batchSize = 20;
            int totalBatches = (int) Math.ceil((double) allRequests.size() / batchSize);

            for (int i = 0; i < totalBatches; i++) {
                int startIndex = i * batchSize;
                int endIndex = Math.min(startIndex + batchSize, allRequests.size());
                List<ProductRequest> batch = allRequests.subList(startIndex, endIndex);

                log.info("处理批次 {}/{}: 商品 {} 到 {}", i + 1, totalBatches, startIndex + 1, endIndex);

                ApiResult<Object> result = tianxinApiClient.batchSyncProducts(batch);

                if (result.isSuccess()) {
                    log.info("批次 {} 处理成功", i + 1);
                } else {
                    log.error("批次 {} 处理失败: {}", i + 1, result.getErrorMessage());
                }

                // 批次间延迟，避免过于频繁的请求
                if (i < totalBatches - 1) {
                    Thread.sleep(1000);
                }
            }

            log.info("所有批次处理完成");

        } catch (Exception e) {
            log.error("分块批量处理异常", e);
        }
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        log.info("开始运行天心API使用示例...");

        basicApiCall();
        batchApiCall();
        queryApiCall();
        errorHandlingExample();
        healthCheckExample();
        circuitBreakerManagement();
        chunkedBatchProcessing();

        log.info("所有示例运行完成");
    }
}
