package org.dromara.common.tianxin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 天心天思API模块枚举
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ApiModule {

    /**
     * 基础资料模块
     */
    BASIC_DATA("basic", "基础资料"),

    /**
     * 商品管理模块
     */
    PRODUCT("product", "商品管理"),

    /**
     * 客户管理模块
     */
    CUSTOMER("customer", "客户管理"),

    /**
     * 供应商管理模块
     */
    SUPPLIER("supplier", "供应商管理"),

    /**
     * 进销存模块
     */
    INVENTORY("inventory", "进销存管理"),

    /**
     * 生产制造模块
     */
    PRODUCTION("production", "生产制造"),

    /**
     * 财务管理模块
     */
    FINANCE("finance", "财务管理"),

    /**
     * 项目管理模块
     */
    PROJECT("project", "项目管理"),

    /**
     * 固定资产模块
     */
    ASSET("asset", "固定资产");

    /**
     * 模块代码
     */
    private final String code;

    /**
     * 模块名称
     */
    private final String name;

    /**
     * 根据代码获取模块
     *
     * @param code 模块代码
     * @return 模块枚举
     */
    public static ApiModule getByCode(String code) {
        for (ApiModule module : values()) {
            if (module.getCode().equals(code)) {
                return module;
            }
        }
        throw new IllegalArgumentException("未知的API模块代码: " + code);
    }
}
