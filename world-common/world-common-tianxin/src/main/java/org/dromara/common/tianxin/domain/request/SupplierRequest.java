package org.dromara.common.tianxin.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 供应商信息请求对象
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierRequest extends BaseRequest {

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商简称
     */
    private String shortName;

    /**
     * 供应商类型：1-原材料，2-设备，3-服务
     */
    private Integer supplierType;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 供应商地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 供应商等级：1-A级，2-B级，3-C级
     */
    private Integer supplierLevel;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 付款周期（天）
     */
    private Integer paymentPeriod;

    /**
     * 供应商状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;
}
