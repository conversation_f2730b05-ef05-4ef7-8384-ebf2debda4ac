package org.dromara.common.tianxin.exception;

/**
 * 天心天思API认证异常
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public class TianxinAuthException extends TianxinApiException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造方法
     *
     * @param message 错误消息
     */
    public TianxinAuthException(String message) {
        super("AUTH_ERROR", message);
    }

    /**
     * 构造方法
     *
     * @param message 错误消息
     * @param cause   原因
     */
    public TianxinAuthException(String message, Throwable cause) {
        super("AUTH_ERROR", message, cause);
    }
}
