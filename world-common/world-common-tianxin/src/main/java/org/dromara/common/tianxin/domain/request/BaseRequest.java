package org.dromara.common.tianxin.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 天心天思API基础请求对象
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
public class BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime requestTime;

    /**
     * 操作类型：ADD-新增，UPDATE-修改，DELETE-删除，QUERY-查询
     */
    private String operationType;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 备注信息
     */
    private String remark;

    public BaseRequest() {
        this.requestTime = LocalDateTime.now();
        this.requestId = generateRequestId();
    }

    /**
     * 生成请求ID
     *
     * @return 请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
}
