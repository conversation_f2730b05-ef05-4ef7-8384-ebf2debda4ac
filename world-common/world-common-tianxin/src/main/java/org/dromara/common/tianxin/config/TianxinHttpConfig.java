package org.dromara.common.tianxin.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 天心API HTTP配置
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Slf4j
@Configuration
@EnableAsync
public class TianxinHttpConfig {

    @Autowired
    private TianxinProperties properties;

    /**
     * 天心API异步执行器
     *
     * @return 线程池执行器
     */
    @Bean("tianxinApiExecutor")
    public Executor tianxinApiExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(properties.getThreadPoolSize());

        // 最大线程数
        executor.setMaxPoolSize(properties.getThreadPoolSize() * 2);

        // 队列容量
        executor.setQueueCapacity(200);

        // 线程名前缀
        executor.setThreadNamePrefix("TianxinAPI-");

        // 线程空闲时间
        executor.setKeepAliveSeconds(60);

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();

        log.info("天心API线程池初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}",
                properties.getThreadPoolSize(), properties.getThreadPoolSize() * 2, 200);

        return executor;
    }
}
