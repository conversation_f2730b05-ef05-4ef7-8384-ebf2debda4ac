package org.dromara.common.tianxin.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品信息请求对象
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductRequest extends BaseRequest {

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String specification;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 商品分类
     */
    private String category;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 标准价格
     */
    private BigDecimal standardPrice;

    /**
     * 成本价格
     */
    private BigDecimal costPrice;

    /**
     * 销售价格
     */
    private BigDecimal salePrice;

    /**
     * 库存数量
     */
    private BigDecimal stockQuantity;

    /**
     * 安全库存
     */
    private BigDecimal safetyStock;

    /**
     * 商品状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;
}
