package org.dromara.common.tianxin.exception;

/**
 * 天心天思API异常
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public class TianxinApiException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 构造方法
     *
     * @param message 错误消息
     */
    public TianxinApiException(String message) {
        super(message);
    }

    /**
     * 构造方法
     *
     * @param errorCode 错误码
     * @param message   错误消息
     */
    public TianxinApiException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造方法
     *
     * @param message 错误消息
     * @param cause   原因
     */
    public TianxinApiException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造方法
     *
     * @param errorCode 错误码
     * @param message   错误消息
     * @param cause     原因
     */
    public TianxinApiException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }
}
