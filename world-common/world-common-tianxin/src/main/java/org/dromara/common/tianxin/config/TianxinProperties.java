package org.dromara.common.tianxin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 天心天思API配置属性
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Component
@ConfigurationProperties(prefix = "tianxin.api")
public class TianxinProperties {

    /**
     * API基础URL
     */
    private String baseUrl = "http://api.amtxts.com";

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;

    /**
     * 重试次数
     */
    private Integer retryTimes = 3;

    /**
     * 重试间隔（毫秒）
     */
    private Long retryInterval = 1000L;

    /**
     * 是否启用API调用
     */
    private Boolean enabled = true;

    /**
     * 批量处理大小
     */
    private Integer batchSize = 100;

    /**
     * 线程池大小
     */
    private Integer threadPoolSize = 10;

    /**
     * 是否启用日志记录
     */
    private Boolean logEnabled = true;

    /**
     * 是否启用缓存
     */
    private Boolean cacheEnabled = true;

    /**
     * 缓存过期时间（秒）
     */
    private Long cacheExpireTime = 3600L;

    // ==================== 熔断器配置 ====================

    /**
     * 是否启用熔断器
     */
    private Boolean circuitBreakerEnabled = true;

    /**
     * 熔断器失败阈值
     */
    private Integer circuitBreakerFailureThreshold = 5;

    /**
     * 熔断器超时时间（毫秒）
     */
    private Long circuitBreakerTimeout = 60000L;

    /**
     * 半开状态最大请求数
     */
    private Integer circuitBreakerHalfOpenMaxCalls = 3;

    // ==================== 连接池配置 ====================

    /**
     * 最大连接数
     */
    private Integer maxConnections = 100;

    /**
     * 每个路由的最大连接数
     */
    private Integer maxConnectionsPerRoute = 20;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 30000;

    /**
     * 连接保持活跃时间（毫秒）
     */
    private Integer keepAliveTime = 30000;

    // ==================== 健康检查配置 ====================

    /**
     * 是否启用健康检查
     */
    private Boolean healthCheckEnabled = true;

    /**
     * 健康检查间隔（秒）
     */
    private Integer healthCheckInterval = 300;

    // ==================== 限流配置 ====================

    /**
     * 是否启用限流
     */
    private Boolean rateLimitEnabled = true;

    /**
     * 每秒最大请求数
     */
    private Integer rateLimitPerSecond = 100;

    /**
     * 每分钟最大请求数
     */
    private Integer rateLimitPerMinute = 1000;
}
