package org.dromara.common.sap.util;

import com.sap.conn.jco.JCoException;
import com.sap.conn.jco.JCoStructure;
import com.sap.conn.jco.JCoTable;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.sap.service.SapService;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SAP工具类，提供便捷的调用方式
 * <AUTHOR>
 */
@Slf4j
public class SapUtils {

    /**
     * 获取SAP服务实例
     */
    private static SapService getSapService() {
        return SpringUtils.getBean(SapService.class);
    }

    /**
     * 调用SAP函数（无参数）
     *
     * @param functionName SAP函数名称
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunction(String functionName) throws JCoException {
        return callFunction(functionName, null);
    }

    /**
     * 调用SAP函数（单个参数）
     *
     * @param functionName SAP函数名称
     * @param paramName    参数名称
     * @param paramValue   参数值
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunction(String functionName, String paramName, Object paramValue)
        throws JCoException {
        Map<String, Object> params = new HashMap<>();
        params.put(paramName, paramValue);
        return callFunction(functionName, params);
    }

    /**
     * 调用SAP函数（多个参数）
     *
     * @param functionName SAP函数名称
     * @param params       参数Map
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunction(String functionName, Map<String, Object> params)
        throws JCoException {
        return getSapService().callFunction(functionName, params);
    }

    /**
     * 调用SAP函数（带表格参数）
     *
     * @param functionName SAP函数名称
     * @param params       输入参数
     * @param tableName    表格参数名称
     * @param tableData    表格数据
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunctionWithTable(String functionName, Map<String, Object> params,
                                                            String tableName, Object[] tableData) throws JCoException {
        Map<String, Object[]> tableParams = new HashMap<>();
        tableParams.put(tableName, tableData);
        return getSapService().callFunctionWithTable(functionName, params, tableParams);
    }

    /**
     * 调用SAP函数（带多个表格参数）
     *
     * @param functionName SAP函数名称
     * @param params       输入参数
     * @param tableParams  表格参数Map
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunctionWithTable(String functionName, Map<String, Object> params,
                                                            Map<String, Object[]> tableParams) throws JCoException {
        return getSapService().callFunctionWithTable(functionName, params, tableParams);
    }

    /**
     * 调用SAP函数（带表格参数）
     *
     * @param functionName SAP函数名称
     * @param tableName    表格参数名称
     * @param tableData    表格数据
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunctionWithTable(String functionName,
                                                            String tableName, Object[] tableData) throws JCoException {
        Map<String, Object[]> tableParams = new HashMap<>();
        tableParams.put(tableName, tableData);
        return getSapService().callFunctionWithTable(functionName, tableParams);
    }

    /**
     * 从SAP返回结果中获取指定参数值
     *
     * @param result    SAP返回结果
     * @param paramName 参数名称
     * @return 参数值
     */
    public static Object getParameter(Map<String, Object> result, String paramName) {
        if (result == null) {
            log.warn("SAP返回结果为null，无法获取参数: {}", paramName);
            return null;
        }

        Object value = result.get(paramName);
        if (value == null) {
            log.debug("SAP返回结果中未找到参数: {}", paramName);
            // 检查是否有类似的参数名
            for (String key : result.keySet()) {
                if (key.contains(paramName) || paramName.contains(key)) {
                    log.debug("发现可能的相似参数: {}", key);
                }
            }
        }
        return value;
    }

    /**
     * 从SAP返回结果中获取指定参数值（字符串类型）
     *
     * @param result    SAP返回结果
     * @param paramName 参数名称
     * @return 参数值
     */
    public static String getStringParameter(Map<String, Object> result, String paramName) {
        Object value = getParameter(result, paramName);
        return value != null ? value.toString() : null;
    }

    /**
     * 从SAP返回结果中获取指定参数值（整数类型）
     *
     * @param result    SAP返回结果
     * @param paramName 参数名称
     * @return 参数值
     */
    public static Integer getIntegerParameter(Map<String, Object> result, String paramName) {
        Object value = getParameter(result, paramName);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法将参数 {} 转换为整数: {}", paramName, value);
            return null;
        }
    }

    /**
     * 从SAP返回结果中获取指定参数值（长整数类型）
     *
     * @param result    SAP返回结果
     * @param paramName 参数名称
     * @return 参数值
     */
    public static Long getLongParameter(Map<String, Object> result, String paramName) {
        Object value = getParameter(result, paramName);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法将参数 {} 转换为长整数: {}", paramName, value);
            return null;
        }
    }

    /**
     * 从SAP返回结果中获取指定参数值（双精度浮点类型）
     *
     * @param result    SAP返回结果
     * @param paramName 参数名称
     * @return 参数值
     */
    public static Double getDoubleParameter(Map<String, Object> result, String paramName) {
        Object value = getParameter(result, paramName);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法将参数 {} 转换为双精度浮点数: {}", paramName, value);
            return null;
        }
    }

    /**
     * 从SAP返回结果中获取指定参数值（布尔类型）
     *
     * @param result    SAP返回结果
     * @param paramName 参数名称
     * @return 参数值
     */
    public static Boolean getBooleanParameter(Map<String, Object> result, String paramName) {
        Object value = getParameter(result, paramName);
        if (value == null) {
            return null;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        String strValue = value.toString().toUpperCase();
        return "X".equals(strValue) || "TRUE".equals(strValue) || "1".equals(strValue) || "YES".equals(strValue);
    }

    /**
     * 从SAP返回结果中获取表格数据
     *
     * @param result    SAP返回结果
     * @param tableName 表格名称
     * @return 表格数据
     */
    @SuppressWarnings("unchecked")
    public static Object[] getTableData(Map<String, Object> result, String tableName) {
        if (result == null) {
            log.warn("SAP返回结果为null，无法获取表格: {}", tableName);
            return null;
        }

        log.debug("尝试获取表格: {}, 可用参数: {}", tableName, result.keySet());

        Object value = getParameter(result, tableName);
        if (value == null) {
            // 尝试查找可能的表格名称
            log.warn("未找到表格: {}，尝试查找相似的表格名称", tableName);
            for (String key : result.keySet()) {
                if (key.contains(tableName) || tableName.contains(key)) {
                    log.info("发现可能的相似表格名称: {}", key);
                }
            }
            return null;
        }

        if (value instanceof Object[]) {
            Object[] tableData = (Object[]) value;
            log.debug("成功获取表格: {}, 行数: {}", tableName, tableData.length);

            // 检查表格数据是否为空
            if (tableData.length == 0) {
                log.warn("表格 {} 没有数据", tableName);
            } else {
                // 记录第一行数据的结构，帮助诊断问题
                try {
                    if (tableData[0] instanceof Map) {
                        Map<String, Object> firstRow = (Map<String, Object>) tableData[0];
                        log.debug("表格 {} 第一行数据结构: {}", tableName, firstRow.keySet());
                    } else {
                        log.debug("表格 {} 第一行数据类型: {}", tableName, tableData[0].getClass().getName());
                    }
                } catch (Exception e) {
                    log.warn("记录表格 {} 数据结构时发生异常: {}", tableName, e.getMessage());
                }
            }

            return tableData;
        } else {
            log.warn("表格 {} 不是数组类型: {}", tableName, value.getClass().getName());
            return null;
        }
    }

    /**
     * 打印SAP返回结果中的所有参数
     *
     * @param result SAP返回结果
     */
    public static void printAllParameters(Map<String, Object> result) {
        if (result == null) {
            log.warn("SAP返回结果为null");
            return;
        }
        log.info("SAP返回结果中的所有参数:");
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Object[]) {
                Object[] array = (Object[]) value;
                log.info("{}: [数组, 长度: {}]", key, array.length);

                // 打印数组的前几个元素
                for (int i = 0; i < Math.min(array.length, 3); i++) {
                    log.info("  {}[{}]: {}", key, i, array[i]);
                }

                if (array.length > 3) {
                    log.info("  ... 还有 {} 个元素", array.length - 3);
                }
            } else {
                log.info("{}: {}", key, value);
            }
        }
    }

    /**
     * 创建表格行数据
     *
     * @param values 键值对数组，格式为：key1, value1, key2, value2, ...
     * @return 表格行数据
     */
    public static Map<String, Object> createTableRow(Object... values) {
        if (values == null || values.length % 2 != 0) {
            throw new IllegalArgumentException("参数数量必须为偶数，格式为：key1, value1, key2, value2, ...");
        }

        Map<String, Object> row = new HashMap<>();
        for (int i = 0; i < values.length; i += 2) {
            if (values[i] instanceof String) {
                row.put((String) values[i], values[i + 1]);
            } else {
                throw new IllegalArgumentException("键必须是字符串类型");
            }
        }
        return row;
    }

    /**
     * 调用SAP函数并回调通知结果
     *
     * @param functionName         主SAP函数名称
     * @param params               主函数参数
     * @param tableName            主函数表格名称
     * @param tableData            主函数表格数据
     * @param callbackFunctionName 回调SAP函数名称
     * @param callbackParams       回调函数参数
     * @param successParamName     成功状态参数名称
     * @param messageParamName     消息参数名称
     * @return 主函数返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunctionWithCallback(
        String functionName,
        Map<String, Object> params,
        String tableName,
        Object[] tableData,
        String callbackFunctionName,
        Map<String, Object> callbackParams,
        String successParamName,
        String messageParamName) throws JCoException {

        // 调用主SAP函数
        Map<String, Object> result = callFunctionWithTable(functionName, params, tableName, tableData);

        // 处理回调
        processCallback(result, callbackFunctionName, callbackParams, successParamName, messageParamName);

        return result;
    }

    /**
     * 调用SAP函数并回调通知结果（简化版）
     *
     * @param functionName         主SAP函数名称
     * @param tableName            主函数表格名称
     * @param tableData            主函数表格数据
     * @param callbackFunctionName 回调SAP函数名称
     * @param successParamName     成功状态参数名称
     * @param messageParamName     消息参数名称
     * @return 主函数返回结果
     * @throws JCoException SAP调用异常
     */
    public static Map<String, Object> callFunctionWithCallback(
        String functionName,
        String tableName,
        Object[] tableData,
        String callbackFunctionName,
        String successParamName,
        String messageParamName) throws JCoException {

        return callFunctionWithCallback(
            functionName,
            null,
            tableName,
            tableData,
            callbackFunctionName,
            null,
            successParamName,
            messageParamName);
    }

    /**
     * 处理SAP回调
     *
     * @param result               主函数返回结果
     * @param callbackFunctionName 回调SAP函数名称
     * @param callbackParams       回调函数参数
     * @param successParamName     成功状态参数名称
     * @param messageParamName     消息参数名称
     * @throws JCoException SAP调用异常
     */
    private static void processCallback(
        Map<String, Object> result,
        String callbackFunctionName,
        Map<String, Object> callbackParams,
        String successParamName,
        String messageParamName) throws JCoException {

        if (result == null || callbackFunctionName == null) {
            log.warn("无法处理回调: 结果为空或回调函数名称为空");
            return;
        }

        // 准备回调参数
        Map<String, Object> params = callbackParams != null ? new HashMap<>(callbackParams) : new HashMap<>();

        // 从主函数结果中提取成功状态和消息
        Object successValue = getParameter(result, successParamName);
        Object messageValue = getParameter(result, messageParamName);

        // 添加到回调参数中
        params.put("SUCCESS", successValue != null ? successValue : "E");
        params.put("MESSAGE", messageValue != null ? messageValue : "未获取到消息");

        // 调用回调函数
        log.info("调用回调函数: {}, 参数: {}", callbackFunctionName, params);
        Map<String, Object> callbackResult = callFunction(callbackFunctionName, params);

        // 记录回调结果
        if (callbackResult != null) {
            log.info("回调函数执行结果: {}", callbackResult);
        } else {
            log.warn("回调函数执行结果为空");
        }
    }

    /**
     * 调用SAP函数并转换为指定类型的对象列表
     *
     * @param functionName SAP函数名称
     * @param params       输入参数
     * @param tableName    表格名称
     * @param targetClass  目标类型
     * @return 对象列表
     * @throws JCoException SAP调用异常
     */
    public static <T> List<T> callFunctionAndConvertToList(String functionName, Map<String, Object> params,
                                                           String tableName, Class<T> targetClass) throws JCoException {
        Map<String, Object> result = callFunction(functionName, params);
        JCoTable table = (JCoTable) result.get(tableName);
        if (table == null) {
            return Collections.emptyList();
        }
        try {
            return SapFieldConverter.convert(table, targetClass);
        } catch (Exception e) {
            log.error("转换SAP表格数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换SAP表格数据失败", e);
        }
    }

    /**
     * 调用SAP函数并转换为指定类型的对象
     *
     * @param functionName SAP函数名称
     * @param params       输入参数
     * @param targetClass  目标类型
     * @return 对象
     * @throws JCoException SAP调用异常
     */
    public static <T> T callFunctionAndConvert(String functionName, Map<String, Object> params,
                                               Class<T> targetClass) throws JCoException {
        Map<String, Object> result = callFunction(functionName, params);
        JCoStructure structure = (JCoStructure) result.get("ES_RETURN");
        if (structure == null) {
            return null;
        }
        try {
            return SapFieldConverter.convert(structure, targetClass);
        } catch (Exception e) {
            log.error("转换SAP结构数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换SAP结构数据失败", e);
        }
    }

    /**
     * 调用带表格参数的SAP函数并转换为指定类型的对象列表
     *
     * @param functionName SAP函数名称
     * @param params       输入参数
     * @param tableParams  表格参数
     * @param tableName    返回表格名称
     * @param targetClass  目标类型
     * @return 对象列表
     * @throws JCoException SAP调用异常
     */
    public static <T> List<T> callFunctionWithTableAndConvert(String functionName, Map<String, Object> params,
                                                              Map<String, Object[]> tableParams, String tableName, Class<T> targetClass) throws JCoException {
        Map<String, Object> result = callFunctionWithTable(functionName, params, tableParams);
        JCoTable table = (JCoTable) result.get(tableName);
        if (table == null) {
            return Collections.emptyList();
        }
        try {
            return SapFieldConverter.convert(table, targetClass);
        } catch (Exception e) {
            log.error("转换SAP表格数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("转换SAP表格数据失败", e);
        }
    }
}
