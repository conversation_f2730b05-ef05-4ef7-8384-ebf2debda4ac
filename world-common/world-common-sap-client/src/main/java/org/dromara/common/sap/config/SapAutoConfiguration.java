package org.dromara.common.sap.config;

import org.dromara.common.sap.pool.SapConnectionPool;
import org.dromara.common.sap.service.SapService;
import org.dromara.common.sap.service.impl.SapServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(SapProperties.class)
@ComponentScan(basePackages = "org.dromara.common.sap")
public class SapAutoConfiguration {
    // 自动配置类，用于启用SAP相关配置

    @Bean
    @ConditionalOnMissingBean
    public SapConnectionPool sapConnectionPool(SapProperties sapProperties) {
        return new SapConnectionPool(sapProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    public SapService sapService(SapConnectionPool sapConnectionPool) {
        return new SapServiceImpl(sapConnectionPool);
    }
}
