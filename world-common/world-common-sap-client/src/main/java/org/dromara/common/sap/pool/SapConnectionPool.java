package org.dromara.common.sap.pool;

import cn.hutool.core.convert.Convert;
import com.sap.conn.jco.JCoDestination;
import com.sap.conn.jco.JCoDestinationManager;
import com.sap.conn.jco.JCoException;
import com.sap.conn.jco.ext.DestinationDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.sap.config.SapProperties;
import org.springframework.stereotype.Component;
import jakarta.annotation.PreDestroy;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class SapConnectionPool {

    private final SapProperties sapProperties;
    private static final String DESTINATION_NAME = "SAP_POOL";
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private volatile boolean isShuttingDown = false;

    public SapConnectionPool(SapProperties sapProperties) {
        this.sapProperties = sapProperties;
        initializePool();
        log.info("SAP连接池初始化完成，最大连接数: {}, 峰值限制: {}",
                sapProperties.getPoolSize(), sapProperties.getPeakLimit());
    }

    private void initializePool() {
        try {
            Properties connectProperties = new Properties();
            connectProperties.setProperty(DestinationDataProvider.JCO_ASHOST, sapProperties.getAshost());
            connectProperties.setProperty(DestinationDataProvider.JCO_SYSNR, sapProperties.getSystemNumber());
            connectProperties.setProperty(DestinationDataProvider.JCO_CLIENT, sapProperties.getClient());
            connectProperties.setProperty(DestinationDataProvider.JCO_USER, sapProperties.getUser());
            connectProperties.setProperty(DestinationDataProvider.JCO_PASSWD, sapProperties.getPasswd());
            connectProperties.setProperty(DestinationDataProvider.JCO_LANG, sapProperties.getLanguage());
            connectProperties.setProperty(DestinationDataProvider.JCO_POOL_CAPACITY,
                    Convert.toStr(sapProperties.getPoolSize()));
            connectProperties.setProperty(DestinationDataProvider.JCO_PEAK_LIMIT,
                    Convert.toStr(sapProperties.getPeakLimit()));

            CustomDestinationDataProvider provider = new CustomDestinationDataProvider();
            provider.addDestinationProperties(DESTINATION_NAME, connectProperties);
            createDataFile(sapProperties.getAbap_as_pooled(), "jcoDestination", connectProperties);
            log.info("SAP连接池配置完成，目标系统: {}:{}", sapProperties.getAshost(), sapProperties.getSystemNumber());
        } catch (Exception e) {
            log.error("SAP连接池初始化失败", e);
            throw new RuntimeException("SAP连接池初始化失败", e);
        }
    }

    /**
     * 创建SAP接口属性文件。
     *
     * @param name       ABAP管道名称
     * @param suffix     属性文件后缀
     * @param properties 属性文件内容
     */
    private static void createDataFile(String name, String suffix, Properties properties) {
        File cfg = new File(name + "." + suffix);
        if (cfg.exists()) {
            cfg.deleteOnExit();
        }
        try {
            FileOutputStream fos = new FileOutputStream(cfg, false);
            properties.store(fos, "for tests only !");
            fos.close();
        } catch (Exception e) {
            throw new RuntimeException("Unable to create the destination file " + cfg.getName(), e);
        }finally {
            cfg.deleteOnExit();
        }
    }

    public JCoDestination getDestination() throws JCoException {
        if (isShuttingDown) {
            throw new JCoException(0, "系统正在关闭，拒绝新的SAP连接请求");
        }
        int currentConnections = activeConnections.incrementAndGet();
        log.debug("获取SAP连接，当前活动连接数: {}", currentConnections);
        try {
            JCoDestination destination = JCoDestinationManager.getDestination(DESTINATION_NAME);
            log.info("成功获取SAP连接，目标系统: {}", destination.getDestinationID());
            return destination;
        } catch (JCoException e) {
            activeConnections.decrementAndGet();
            log.error("获取SAP连接失败", e);
            throw e;
        }
    }

    /**
     * 释放SAP连接
     * 注意：JCoDestination本身是线程安全的，不需要显式关闭
     * 但我们可以记录连接释放的日志
     */
    public void releaseConnection() {
        int currentConnections = activeConnections.decrementAndGet();
        log.debug("释放SAP连接，当前活动连接数: {}", currentConnections);
    }

    /**
     * 系统关闭前执行清理操作
     */
    @PreDestroy
    public void shutdown() {
        isShuttingDown = true;
        // 等待所有活动连接释放
        int retryCount = 0;
        while (activeConnections.get() > 0 && retryCount < 10) {
            log.info("等待SAP连接释放，当前活动连接数: {}", activeConnections.get());
            try {
                Thread.sleep(1000);
                retryCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.info("SAP连接池已关闭，最终活动连接数: {}", activeConnections.get());
    }
}
