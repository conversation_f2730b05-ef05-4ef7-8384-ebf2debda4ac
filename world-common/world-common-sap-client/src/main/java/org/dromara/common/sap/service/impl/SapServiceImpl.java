package org.dromara.common.sap.service.impl;

import com.sap.conn.jco.*;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.sap.pool.SapConnectionPool;
import org.dromara.common.sap.service.SapService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SapServiceImpl implements SapService {

    private final SapConnectionPool sapConnectionPool;

    public SapServiceImpl(SapConnectionPool sapConnectionPool) {
        this.sapConnectionPool = sapConnectionPool;
    }

    /**
     * 调用SAP函数
     *
     * @param functionName SAP函数名称
     * @param params       函数参数，Map<String, Object>类型
     * @return 函数的返回值，Map<String, Object>类型
     * @throws JCoException 如果调用SAP函数时发生异常，则抛出此异常
     */
    @Override
    public Map<String, Object> callFunction(String functionName, Map<String, Object> params) throws JCoException {
        log.info("开始调用SAP函数: {}, 参数: {}", functionName, params);
        long startTime = System.currentTimeMillis();

        JCoDestination destination = null;
        try {
            destination = sapConnectionPool.getDestination();
            JCoFunction function = destination.getRepository().getFunction(functionName);
            if (function == null) {
                String errorMsg = "SAP函数 " + functionName + " 不存在";
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 设置输入参数
            getImportParameterList(params, function);

            // 执行函数
            log.debug("执行SAP函数: {}", functionName);
            function.execute(destination);
            log.debug("SAP函数执行完成: {}", functionName);

            // 获取输出参数
            Map<String, Object> result = getOutputParameters(function);
            long endTime = System.currentTimeMillis();
            log.info("SAP函数调用完成: {}, 耗时: {}ms",
                functionName, (endTime - startTime));

            return result;
        } catch (JCoException e) {
            log.error("调用SAP函数失败: {}, 错误: {}", functionName, e.getMessage(), e);
            throw e;
        } finally {
            if (destination != null) {
                sapConnectionPool.releaseConnection();
            }
        }
    }

    /**
     * 调用带有表格参数的SAP函数。
     *
     * @param functionName SAP函数的名称。
     * @param tableParams  表格参数，每个表格参数是一个键值对，键为表格名称，值为表格数据（对象数组）。
     * @return 包含SAP函数输出参数的Map，键为参数名称，值为参数值。
     * @throws JCoException 如果调用SAP函数失败，则抛出此异常。
     */
    @Override
    public Map<String, Object> callFunctionWithTable(String functionName,
                                                     Map<String, Object[]> tableParams) throws JCoException {
        log.info("开始调用带表格参数的SAP函数: {}, 表格参数数量: {}",
            functionName, tableParams != null ? tableParams.size() : 0);
        long startTime = System.currentTimeMillis();

        JCoDestination destination = null;
        try {
            destination = sapConnectionPool.getDestination();
            JCoFunction function = destination.getRepository().getFunction(functionName);
            if (function == null) {
                String errorMsg = "SAP函数 " + functionName + " 不存在";
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
            log.info("成功获取SAP函数: {}", functionName);
            // 设置表格参数
            if (tableParams != null) {
                for (Map.Entry<String, Object[]> entry : tableParams.entrySet()) {
                    String tableName = entry.getKey();
                    Object[] tableData = entry.getValue();

                    JCoTable table = function.getTableParameterList().getTable(tableName);
                    if (table == null) {
                        log.error("表格参数 {} 不存在于SAP函数 {}", tableName, functionName);
                        // 列出所有可用的表格参数
                        JCoParameterList tables = function.getTableParameterList();
                        if (tables != null) {
                            log.info("SAP函数 {} 可用的表格参数: ", functionName);
                            for (JCoField field : tables) {
                                log.info("  - {}", field.getName());
                            }
                        }
                        throw new RuntimeException("表格参数 " + tableName + " 不存在于SAP函数 " + functionName);
                    }

                    log.debug("设置表格参数: {}, 行数: {}", tableName, tableData.length);

                    for (Object row : tableData) {
                        table.appendRow();
                        if (row instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> rowMap = (Map<String, Object>) row;
                            for (Map.Entry<String, Object> field : rowMap.entrySet()) {
                                try {
                                    table.setValue(field.getKey(), field.getValue());
                                    log.debug("设置表格行数据: {} = {}", field.getKey(), field.getValue());
                                } catch (Exception e) {
                                    log.error("设置表格行数据失败: {} = {}, 错误: {}",
                                        field.getKey(), field.getValue(), e.getMessage());
                                    // 列出表格的所有可用字段
                                    log.info("表格 {} 可用的字段: ", tableName);
                                    for (JCoField tableField : table) {
                                        log.info("  - {}: {}", tableField.getName(), tableField.getTypeAsString());
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 执行函数
            function.execute(destination);
            // 获取输出参数
            Map<String, Object> result = getOutputParameters(function);
            long endTime = System.currentTimeMillis();
            log.info("SAP函数调用完成: {}, 耗时: {}ms", functionName, (endTime - startTime));
            return result;
        } catch (JCoException e) {
            log.error("调用SAP函数失败: {}, 错误: {}", functionName, e.getMessage(), e);
            throw e;
        } finally {
            if (destination != null) {
                sapConnectionPool.releaseConnection();
            }
        }
    }

    /**
     * 调用带有表格参数的SAP函数。
     *
     * @param functionName SAP函数名称
     * @param params       普通参数
     * @param tableParams  表格参数
     * @return SAP函数的返回值
     * @throws JCoException 如果调用SAP函数失败，则抛出该异常
     */
    @Override
    public Map<String, Object> callFunctionWithTable(String functionName, Map<String, Object> params,
                                                     Map<String, Object[]> tableParams) throws JCoException {
        log.info("开始调用带表格参数的SAP函数: {}, 参数: {}, 表格参数数量: {}",
            functionName, params, tableParams != null ? tableParams.size() : 0);
        long startTime = System.currentTimeMillis();

        JCoDestination destination = null;
        try {
            destination = sapConnectionPool.getDestination();
            JCoFunction function = destination.getRepository().getFunction(functionName);
            if (function == null) {
                String errorMsg = "SAP函数 " + functionName + " 不存在";
                throw new RuntimeException(errorMsg);
            }
            log.info("成功获取SAP函数: {}", functionName);
            // 设置输入参数
            getImportParameterList(params, function);
            // 设置表格参数
            if (tableParams != null) {
                for (Map.Entry<String, Object[]> entry : tableParams.entrySet()) {
                    String tableName = entry.getKey();
                    Object[] tableData = entry.getValue();

                    JCoTable table = function.getTableParameterList().getTable(tableName);
                    if (table == null) {
                        log.error("表格参数 {} 不存在于SAP函数 {}", tableName, functionName);
                        // 列出所有可用的表格参数
                        JCoParameterList tables = function.getTableParameterList();
                        if (tables != null) {
                            log.info("SAP函数 {} 可用的表格参数: ", functionName);
                            for (JCoField field : tables) {
                                log.info("  - {}", field.getName());
                            }
                        }
                        throw new RuntimeException("表格参数 " + tableName + " 不存在于SAP函数 " + functionName);
                    }
                    for (Object row : tableData) {
                        table.appendRow();
                        if (row instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> rowMap = (Map<String, Object>) row;
                            for (Map.Entry<String, Object> field : rowMap.entrySet()) {
                                try {
                                    table.setValue(field.getKey(), field.getValue());
                                    log.debug("设置表格行数据: {} = {}", field.getKey(), field.getValue());
                                } catch (Exception e) {
                                    log.error("设置表格行数据失败: {} = {}, 错误: {}",
                                        field.getKey(), field.getValue(), e.getMessage());
                                    // 列出表格的所有可用字段
                                    log.info("表格 {} 可用的字段: ", tableName);
                                    for (JCoField tableField : table) {
                                        log.info("  - {}: {}", tableField.getName(), tableField.getTypeAsString());
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 执行函数
            log.debug("执行SAP函数: {}", functionName);
            function.execute(destination);
            log.debug("SAP函数执行完成: {}", functionName);

            // 获取输出参数
            Map<String, Object> result = getOutputParameters(function);
            long endTime = System.currentTimeMillis();
            log.info("SAP函数调用完成: {}, 耗时: {}ms",
                functionName, (endTime - startTime));

            return result;
        } catch (JCoException e) {
            log.error("调用SAP函数失败: {}, 错误: {}", functionName, e.getMessage(), e);
            throw e;
        } finally {
            if (destination != null) {
                sapConnectionPool.releaseConnection();
            }
        }
    }

    /**
     * 从参数映射中获取输入参数列表并设置到JCoFunction中
     *
     * @param params   包含输入参数的映射
     * @param function 需要设置输入参数的JCoFunction对象
     */
    private void getImportParameterList(Map<String, Object> params, JCoFunction function) {
        JCoParameterList input = function.getImportParameterList();
        if (input != null && params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                input.setValue(entry.getKey(), entry.getValue());
                log.debug("设置输入参数: {} = {}", entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 从JCoFunction获取输出参数并封装成Map返回
     *
     * @param function JCoFunction对象，包含了远程函数调用的相关信息
     * @return 包含输出参数的Map，键为参数名称，值为参数值
     */
    private Map<String, Object> getOutputParameters(JCoFunction function) {
        Map<String, Object> result = new HashMap<>();

        // 获取导出参数
        JCoParameterList exports = function.getExportParameterList();
        if (exports != null) {
            log.debug("获取导出参数，数量: {}", exports.getFieldCount());
            for (JCoField field : exports) {
                result.put(field.getName(), field.getValue());
                log.debug("获取导出参数: {} = {}", field.getName(), field.getValue());
            }
        } else {
            log.debug("没有导出参数");
        }

        // 获取表格参数
        JCoParameterList tables = function.getTableParameterList();
        if (tables != null) {
            log.debug("获取表格参数，数量: {}", tables.getFieldCount());
            for (JCoField field : tables) {
                String tableName = field.getName();
                JCoTable table = tables.getTable(tableName);
                int rowCount = table.getNumRows();
                log.debug("获取表格参数: {}, 行数: {}", tableName, rowCount);

                if (rowCount > 0) {
                    // 记录表格的第一行数据结构，帮助诊断问题
                    table.firstRow();
                    log.debug("表格 {} 第一行数据结构: ", tableName);
                    for (JCoField tableField : table) {
                        log.debug("  - {}: {}", tableField.getName(), tableField.getValue());
                    }
                }

                result.put(tableName, convertTableToList(table));
            }
        } else {
            log.debug("没有表格参数");
        }

        return result;
    }

    /**
     * 将JCoTable对象转换为Object数组。
     *
     * @param table 需要转换的JCoTable对象
     * @return 转换后的Object数组，如果表格没有数据，则返回空数组
     */
    private Object[] convertTableToList(JCoTable table) {
        int rowCount = table.getNumRows();
        if (rowCount == 0) {
            log.debug("表格没有数据");
            return new Object[0];
        }
        Object[] rows = new Object[rowCount];
        table.firstRow();
        for (int i = 0; i < rowCount; i++) {
            try {
                Map<String, Object> row = new HashMap<>();
                for (JCoField field : table) {
                    row.put(field.getName(), field.getValue());
                }
                rows[i] = row;
                table.nextRow();
            } catch (Exception e) {
                log.error("转换第{}行数据时发生异常: {}", i + 1, e.getMessage());
            }
        }
        return rows;
    }
}
