package org.dromara.common.sap.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "sap")
public class SapProperties {

    /**
     * SAP系统编号
     */
    private String systemNumber;

    /**
     * SAP服务器IP地址
     */
    private String ashost;

    /**
     * SAP客户端编号
     */
    private String client;

    /**
     * SAP用户名
     */
    private String user;

    /**
     * SAP密码
     */
    private String passwd;

    /**
     * SAP语言
     */
    private String language;

    /**
     * 最大连接池大小
     */
    private Integer poolSize = 10;

    /**
     * 连接峰值限制
     */
    private Integer peakLimit = 20;

    /**
     * SAP连接池配置文件名称
     */
    private String abap_as_pooled;
}
