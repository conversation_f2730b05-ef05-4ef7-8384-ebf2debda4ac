package org.dromara.common.sap.util;

import com.sap.conn.jco.JCoParameterList;
import com.sap.conn.jco.JCoStructure;
import com.sap.conn.jco.JCoTable;
import org.dromara.common.sap.annotation.SapFiled;
import org.dromara.common.sap.enums.FiledType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SAP字段转换工具类
 */
public class SapFieldConverter {
    private static final Logger logger = LoggerFactory.getLogger(SapFieldConverter.class);

    /**
     * 将JCoTable转换为对象列表
     *
     * @param jCoTable JCoTable对象
     * @param t        目标类型
     * @return 对象列表
     */
    public static <T> List<T> convert(JCoTable jCoTable, Class<T> t)
        throws InstantiationException, IllegalAccessException {
        if (t == null) {
            throw new IllegalArgumentException("Class type cannot be null");
        }

        Integer num = jCoTable.getNumRows();
        List<T> tList = new ArrayList<>(num);
        Map<String, Field> fieldMap = Arrays.stream(t.getDeclaredFields())
            .peek(field -> field.setAccessible(true))
            .collect(Collectors.toMap(Field::getName, Function.identity()));

        for (int i = 0; i < num; i++) {
            jCoTable.setRow(i);
            T cls;
            try {
                cls = t.getDeclaredConstructor().newInstance();
            } catch (NoSuchMethodException | InvocationTargetException e) {
                logger.error("Failed to create instance of class", e);
                throw new InstantiationException("Failed to create instance of class " + t.getName());
            }
            for (Map.Entry<String, Field> entry : fieldMap.entrySet()) {
                Field x = entry.getValue();
                SapFiled sapFiled = x.getAnnotation(SapFiled.class);
                if (sapFiled != null) {
                    Object values = getFieldValues(sapFiled, jCoTable, fieldMap);
                    x.set(cls, values);
                }
            }
            tList.add(cls);
        }
        return tList;
    }

    /**
     * 将JCoStructure转换为对象
     *
     * @param jCoStructure JCoStructure对象
     * @param t            目标类型
     * @return 对象
     */
    public static <T> T convert(JCoStructure jCoStructure, Class<T> t)
        throws InstantiationException, IllegalAccessException {
        T cls;
        try {
            cls = t.getDeclaredConstructor().newInstance();
        } catch (NoSuchMethodException | InvocationTargetException e) {
            logger.error("Failed to create instance of class", e);
            throw new InstantiationException("Failed to create instance of class " + t.getName());
        }
        Field[] field = cls.getClass().getDeclaredFields();
        Arrays.stream(field).forEach(x -> {
            SapFiled sapFiled = x.getAnnotation(SapFiled.class);
            if (sapFiled != null) {
                x.setAccessible(true);
                Object values = getValue(sapFiled.type(), sapFiled.name(), jCoStructure);
                try {
                    x.set(cls, values);
                } catch (IllegalAccessException e) {
                    logger.error("Failed to set field value", e);
                }
            }
        });
        return cls;
    }

    /**
     * 将JCoParameterList转换为对象
     *
     * @param jCoParameterList JCoParameterList对象
     * @param t                目标类型
     * @return 对象
     */
    public static <T> T convert(JCoParameterList jCoParameterList, Class<T> t)
        throws InstantiationException, IllegalAccessException {
        T cls;
        try {
            cls = t.getDeclaredConstructor().newInstance();
        } catch (NoSuchMethodException | InvocationTargetException e) {
            logger.error("Failed to create instance of class", e);
            throw new InstantiationException("Failed to create instance of class " + t.getName());
        }
        Field[] field = cls.getClass().getDeclaredFields();
        Arrays.stream(field).forEach(x -> {
            SapFiled sapFiled = x.getAnnotation(SapFiled.class);
            if (sapFiled != null) {
                x.setAccessible(true);
                Object values = getValue(sapFiled.type(), sapFiled.name(), jCoParameterList);
                try {
                    x.set(cls, values);
                } catch (IllegalAccessException e) {
                    logger.error("Failed to set field value", e);
                }
            }
        });
        return cls;
    }

    private static Object getFieldValues(SapFiled sapFiled, JCoTable jCoTable, Map<String, Field> fieldMap) {
        Object values = null;
        RuntimeException error = null;
        try {
            values = getValue(sapFiled.type(), sapFiled.name(), jCoTable);
        } catch (RuntimeException e) {
            error = e;
        }
        if (values == null && sapFiled.aliasNames().length > 0) {
            for (String aliasName : sapFiled.aliasNames()) {
                try {
                    values = getValue(sapFiled.type(), aliasName, jCoTable);
                    if (values != null) {
                        break;
                    }
                } catch (RuntimeException e) {
                    error = e;
                }
            }
        }
        if (values == null && error != null) {
            throw error;
        }
        return values;
    }

    private static Object getValue(FiledType type, String filed, JCoTable jCoTable) {
        if (FiledType.BIGDECIMAL.equals(type)) {
            return jCoTable.getBigDecimal(filed);
        } else if (FiledType.DATE.equals(type)) {
            return jCoTable.getDate(filed);
        } else if (FiledType.INT.equals(type)) {
            return jCoTable.getInt(filed);
        } else if (FiledType.STRING.equals(type)) {
            return jCoTable.getString(filed);
        }
        return null;
    }

    private static Object getValue(FiledType type, String filed, JCoParameterList jCoParameterList) {
        if (FiledType.BIGDECIMAL.equals(type)) {
            return jCoParameterList.getBigDecimal(filed);
        } else if (FiledType.DATE.equals(type)) {
            return jCoParameterList.getDate(filed);
        } else if (FiledType.INT.equals(type)) {
            return jCoParameterList.getInt(filed);
        } else if (FiledType.STRING.equals(type)) {
            return jCoParameterList.getString(filed);
        }
        return null;
    }

    private static Object getValue(FiledType type, String filed, JCoStructure jCoStructure) {
        if (FiledType.BIGDECIMAL.equals(type)) {
            return jCoStructure.getBigDecimal(filed);
        } else if (FiledType.DATE.equals(type)) {
            return jCoStructure.getDate(filed);
        } else if (FiledType.INT.equals(type)) {
            return jCoStructure.getInt(filed);
        } else if (FiledType.STRING.equals(type)) {
            return jCoStructure.getString(filed);
        }
        return null;
    }
}
