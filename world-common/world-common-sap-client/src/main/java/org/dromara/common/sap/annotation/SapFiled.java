package org.dromara.common.sap.annotation;

import org.dromara.common.sap.enums.FiledType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * SAP字段映射注解
 */
@Target(value = ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SapFiled {
    /**
     * SAP字段名称
     */
    String name();

    /**
     * 字段别名列表
     */
    String[] aliasNames() default {};

    /**
     * 表名
     */
    String table() default "";

    /**
     * 字段类型
     */
    FiledType type() default FiledType.STRING;
}