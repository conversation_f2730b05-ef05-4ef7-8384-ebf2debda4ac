package org.dromara.common.redis.handler;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.config.ISnowflakeCreator;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.utils.IdUtil;
import org.dromara.common.core.utils.NumberUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.util.Collections;

/**
 * Snowflake雪花ID创建器 Redis实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SnowflakeCreatorHand<PERSON> implements ISnowflakeCreator {

    /**
     * 创建Snowflake
     *
     * @return Snowflake
     */
    @Override
    public Snowflake createSnowflake() {
        StringRedisTemplate redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("redis/snowflake_id.lua")));
        redisScript.setResultType(Long.class);
        Long sortId = redisTemplate.execute(redisScript, Collections.singletonList(CacheConstants.SNOWFLAKE_CREATOR_KEY));
        double datacenterId = ObjectUtil.isNotNull(sortId) ? NumberUtil.div(sortId.intValue(), 32, 0, RoundingMode.DOWN) : NumberUtil.Zero;
        int workerId = ObjectUtil.isNotNull(sortId) ? sortId.intValue() % 32 : NumberUtil.Zero;
        log.info("雪花创建成功，sortId:{}, workId:{}, datacenterId：{}", sortId, workerId, datacenterId);
        return IdUtil.getSnowflake(workerId, (int) datacenterId);
    }
}
