package org.dromara.common.core.utils.file;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * ZIP压缩工具类
 * 用于打包处理后的PDF文件和结果说明
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
public class ZipCompressor {

    /**
     * 创建ZIP压缩包
     *
     * @param processedFiles 处理后的文件映射，key为文件名，value为文件内容
     * @param processResult 处理结果说明
     * @return ZIP压缩包的字节数组
     * @throws IOException IO异常
     */
    public static byte[] createZipPackage(Map<String, byte[]> processedFiles, String processResult) throws IOException {
        if (processedFiles == null || processedFiles.isEmpty()) {
            throw new IllegalArgumentException("处理后的文件不能为空");
        }

        log.info("开始创建ZIP压缩包，包含 {} 个文件", processedFiles.size());

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {

            // 设置压缩级别
            zos.setLevel(6);

            // 添加处理后的PDF文件
            for (Map.Entry<String, byte[]> entry : processedFiles.entrySet()) {
                String fileName = entry.getKey();
                byte[] fileContent = entry.getValue();

                // 确保文件名以.pdf结尾
                if (!fileName.toLowerCase().endsWith(".pdf")) {
                    fileName = fileName + ".pdf";
                }

                // 创建ZIP条目
                ZipEntry zipEntry = new ZipEntry(fileName);
                zos.putNextEntry(zipEntry);

                // 写入文件内容
                zos.write(fileContent);
                zos.closeEntry();

                log.debug("添加文件到ZIP: {}, 大小: {} bytes", fileName, fileContent.length);
            }

            // 添加处理结果说明文件
            if (processResult != null && !processResult.trim().isEmpty()) {
                ZipEntry resultEntry = new ZipEntry("处理结果.txt");
                zos.putNextEntry(resultEntry);

                // 写入处理结果说明
                byte[] resultBytes = processResult.getBytes(StandardCharsets.UTF_8);
                zos.write(resultBytes);
                zos.closeEntry();

                log.debug("添加处理结果说明文件，大小: {} bytes", resultBytes.length);
            }

            // 添加处理日志文件
            String logContent = generateProcessLog(processedFiles);
            ZipEntry logEntry = new ZipEntry("处理日志.txt");
            zos.putNextEntry(logEntry);
            zos.write(logContent.getBytes(StandardCharsets.UTF_8));
            zos.closeEntry();

            zos.finish();
            byte[] zipBytes = baos.toByteArray();

            log.info("ZIP压缩包创建完成，总大小: {} bytes", zipBytes.length);
            return zipBytes;

        } catch (IOException e) {
            log.error("创建ZIP压缩包失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 生成处理日志内容
     *
     * @param processedFiles 处理后的文件映射
     * @return 处理日志内容
     */
    private static String generateProcessLog(Map<String, byte[]> processedFiles) {
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append("PDF条形码处理日志\n");
        logBuilder.append("==================\n\n");
        logBuilder.append("处理时间: ").append(DateUtil.date()).append("\n");
        logBuilder.append("处理文件总数: ").append(processedFiles.size()).append("\n\n");

        logBuilder.append("文件处理详情:\n");
        logBuilder.append("------------\n");

        int successCount = 0;
        long totalSize = 0;

        for (Map.Entry<String, byte[]> entry : processedFiles.entrySet()) {
            String fileName = entry.getKey();
            byte[] fileContent = entry.getValue();

            // 确保文件名以.pdf结尾
            if (!fileName.toLowerCase().endsWith(".pdf")) {
                fileName = fileName + ".pdf";
            }

            logBuilder.append("✓ ").append(fileName).append("\n");
            logBuilder.append("  大小: ").append(formatFileSize(fileContent.length)).append("\n");
            logBuilder.append("  状态: 处理成功\n\n");

            successCount++;
            totalSize += fileContent.length;
        }

        logBuilder.append("处理统计:\n");
        logBuilder.append("----------\n");
        logBuilder.append("成功处理: ").append(successCount).append(" 个文件\n");
        logBuilder.append("总大小: ").append(formatFileSize(totalSize)).append("\n");
        logBuilder.append("成功率: 100%\n\n");

        logBuilder.append("注意事项:\n");
        logBuilder.append("----------\n");
        logBuilder.append("1. 条形码已添加到每个PDF文件的右上角\n");
        logBuilder.append("2. 条形码内容基于文件名生成（去除.pdf后缀）\n");
        logBuilder.append("3. 条形码类型: Code128A\n");
        logBuilder.append("4. 条形码位置: 距离右边缘20px，距离上边缘20px\n");
        logBuilder.append("5. 只处理第一页，多页PDF的其他页面保持不变\n");

        return logBuilder.toString();
    }

    /**
     * 格式化文件大小
     *
     * @param bytes 字节数
     * @return 格式化后的文件大小字符串
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 验证ZIP文件是否有效
     *
     * @param zipBytes ZIP文件字节数组
     * @return 是否有效
     */
    public static boolean isValidZip(byte[] zipBytes) {
        if (zipBytes == null || zipBytes.length < 4) {
            return false;
        }

        // 检查ZIP文件头标识
        return zipBytes[0] == 0x50 && zipBytes[1] == 0x4B &&
               zipBytes[2] == 0x03 && zipBytes[3] == 0x04;
    }

    /**
     * 获取ZIP文件大小
     *
     * @param zipBytes ZIP文件字节数组
     * @return 文件大小
     */
    public static long getZipSize(byte[] zipBytes) {
        return zipBytes != null ? zipBytes.length : 0;
    }
}
