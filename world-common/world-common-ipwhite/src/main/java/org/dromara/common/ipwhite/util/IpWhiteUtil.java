package org.dromara.common.ipwhite.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.util.SubnetUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * IP白名单工具类
 *
 * <AUTHOR> Li
 */
@Slf4j
public class IpWhiteUtil {

    /**
     * IP地址缓存，key为IP和白名单的组合，value为是否在白名单中
     */
    private static final ConcurrentHashMap<String, CacheEntry> ipCache = new ConcurrentHashMap<>();

    /**
     * 上次更新时间
     */
    private static long lastUpdateTime = 0;

    /**
     * IPv4地址正则表达式
     */
    private static final Pattern IPV4_PATTERN = Pattern.compile(
        "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$");

    /**
     * IPv6地址正则表达式
     */
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){5}:(?:[0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){4}:(?:[0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){3}:(?:[0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){2}:(?:[0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){6}(?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)))|(?:(?:[0-9A-Fa-f]{1,4}:){0,5}:(?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)))|(?:::(?:[0-9A-Fa-f]{1,4}:){0,5}(?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)))|(?:[0-9A-Fa-f]{1,4}::(?:[0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(?:::(?:[0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(?:(?:[0-9A-Fa-f]{1,4}:){1,7}:))$");

    /**
     * 缓存条目类，包含结果和时间戳
     */
    private static class CacheEntry {
        private final boolean result;
        private final long timestamp;

        public CacheEntry(boolean result) {
            this.result = result;
            this.timestamp = System.currentTimeMillis();
        }

        public boolean isExpired(long expirationTime) {
            return System.currentTimeMillis() - timestamp > expirationTime;
        }
    }

    /**
     * 检查IP是否在白名单中
     *
     * @param ip        要检查的IP地址
     * @param whitelist IP白名单列表
     * @return 是否在白名单中
     */
    public static boolean isIpInWhitelist(String ip, List<String> whitelist) {
        if (StringUtils.isBlank(ip) || whitelist == null || whitelist.isEmpty()) {
            return false;
        }

        // 验证IP地址格式
        if (!isValidIpAddress(ip)) {
            log.error("Invalid IP address format: {}", ip);
            return false;
        }

        // 检查缓存
        String cacheKey = generateCacheKey(ip, whitelist);
        CacheEntry cachedEntry = ipCache.get(cacheKey);
        if (cachedEntry != null && !cachedEntry.isExpired(TimeUnit.MINUTES.toMillis(30))) {
            return cachedEntry.result;
        }

        // 检查IP是否匹配白名单
        boolean result = whitelist.stream().anyMatch(whiteIp -> matchIp(ip, whiteIp));

        // 更新缓存
        ipCache.put(cacheKey, new CacheEntry(result));
        return result;
    }

    /**
     * 清除IP缓存
     */
    public static void clearCache() {
        ipCache.clear();
    }

    /**
     * 清除过期缓存
     *
     * @param expirationTime 过期时间（毫秒）
     */
    public static void clearExpiredCache(long expirationTime) {
        ipCache.entrySet().removeIf(entry -> entry.getValue().isExpired(expirationTime));
    }

    /**
     * 验证IP地址格式
     *
     * @param ip IP地址
     * @return 是否为有效的IP地址
     */
    private static boolean isValidIpAddress(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }
        return IPV4_PATTERN.matcher(ip).matches() || IPV6_PATTERN.matcher(ip).matches();
    }

    /**
     * 生成缓存键
     *
     * @param ip        IP地址
     * @param whitelist 白名单列表
     * @return 缓存键
     */
    private static String generateCacheKey(String ip, List<String> whitelist) {
        return ip + ":" + String.join(",", whitelist);
    }

    /**
     * 匹配IP地址
     *
     * @param ip      要检查的IP地址
     * @param whiteIp 白名单中的IP地址或CIDR
     * @return 是否匹配
     */
    private static boolean matchIp(String ip, String whiteIp) {
        if (StringUtils.isBlank(whiteIp)) {
            return false;
        }

        try {
            if (whiteIp.contains("/")) {
                // CIDR格式
                return matchCidr(ip, whiteIp);
            } else {
                // 精确匹配
                return ip.equals(whiteIp);
            }
        } catch (Exception e) {
            log.error("Error matching IP {} against white IP {}", ip, whiteIp, e);
            return false;
        }
    }

    /**
     * 匹配CIDR格式的IP地址范围
     *
     * @param ip   要检查的IP地址
     * @param cidr CIDR格式的IP地址范围
     * @return 是否在范围内
     */
    private static boolean matchCidr(String ip, String cidr) {
        try {
            SubnetUtils subnetUtils = new SubnetUtils(cidr);
            subnetUtils.setInclusiveHostCount(true);
            return subnetUtils.getInfo().isInRange(ip);
        } catch (IllegalArgumentException e) {
            log.error("Invalid CIDR format: {}", cidr, e);
            return false;
        }
    }

    /**
     * 将IP地址转换为长整型
     *
     * @param ip IP地址
     * @return 长整型表示的IP地址
     */
    private static long ipToLong(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            byte[] bytes = address.getAddress();
            long result = 0;
            for (byte b : bytes) {
                result = result << 8 | (b & 0xFF);
            }
            return result;
        } catch (UnknownHostException e) {
            log.error("Error converting IP to long: {}", ip, e);
            return 0;
        }
    }

    /**
     * 检查是否需要更新缓存
     *
     * @param updateIntervalSeconds 更新间隔（秒）
     * @return 是否需要更新
     */
    public static boolean needUpdate(long updateIntervalSeconds) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUpdateTime > TimeUnit.SECONDS.toMillis(updateIntervalSeconds)) {
            lastUpdateTime = currentTime;
            return true;
        }
        return false;
    }
}
