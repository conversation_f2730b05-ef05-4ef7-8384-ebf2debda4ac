package org.dromara.common.ipwhite.config;

import org.dromara.common.ipwhite.aspectj.IpWhitelistAspect;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConfiguration;

/**
 * IP白名单切面配置类
 *
 * <AUTHOR>
 * @date 2023-06-15
 */
@AutoConfiguration(after = RedisConfiguration.class)
@Configuration
@EnableConfigurationProperties(IpWhiteProperties.class)
public class IpWhitelistAspectConfig {

    @Bean
    public IpWhitelistAspect ipWhitelistAspect(IpWhiteProperties ipWhiteProperties) {
        return new IpWhitelistAspect(ipWhiteProperties);
    }
}
