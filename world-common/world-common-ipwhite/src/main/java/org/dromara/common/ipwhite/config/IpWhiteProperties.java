package org.dromara.common.ipwhite.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "ip-whitelist")
public class IpWhiteProperties {

    /**
     * 是否启用IP白名单
     */
    private boolean enabled = true;

    /**
     * IP白名单列表
     */
    private List<String> ips;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间（秒）
     */
    private long cacheExpireSeconds = 300;

    /**
     * 是否允许本地IP访问
     */
    private boolean allowLocalhost = true;

    /**
     * 是否启用动态更新
     */
    private boolean dynamicUpdate = false;

    /**
     * 动态更新间隔（秒）
     */
    private long updateIntervalSeconds = 60;
}
