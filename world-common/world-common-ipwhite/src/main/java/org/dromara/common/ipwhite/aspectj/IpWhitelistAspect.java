package org.dromara.common.ipwhite.aspectj;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.ipwhite.annotation.IpWhitelist;
import org.dromara.common.ipwhite.config.IpWhiteProperties;
import org.dromara.common.ipwhite.util.IpWhiteUtil;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * AOP 拦截器：检查 IP 白名单
 *
 * <AUTHOR>
 * @date 2025-04-29- 13:47
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class IpWhitelistAspect {
    private final IpWhiteProperties ipWhiteProperties;
    private final List<String> defaultIps = List.of("127.0.0.1", "0:0:0:0:0:0:0:1");

    /**
     * 拦截所有带有 @IpWhitelist 注解的方法，执行 IP 白名单检查
     */
    @Before("@annotation(ipWhitelist)")
    public void checkIpWhitelist(JoinPoint point, IpWhitelist ipWhitelist) {
        // 如果未启用IP白名单，直接放行
        if (!ipWhiteProperties.isEnabled()) {
            return;
        }

        String clientIp = ServletUtils.getClientIP();
        log.debug("正在检查IP白名单，当前访问IP: {}", clientIp);

        // 检查是否需要更新缓存
        if (ipWhiteProperties.isDynamicUpdate() && IpWhiteUtil.needUpdate(ipWhiteProperties.getUpdateIntervalSeconds())) {
            IpWhiteUtil.clearCache();
        }

        // 获取注解中的白名单
        Set<String> annotationWhitelist = ipWhitelist != null && ipWhitelist.value().length > 0
            ? Set.of(ipWhitelist.value())
            : Collections.emptySet();

        // 检查IP是否在白名单中
        boolean isIpAllowed = false;

        // 检查本地IP
        if (ipWhiteProperties.isAllowLocalhost() && defaultIps.contains(clientIp)) {
            isIpAllowed = true;
        }

        // 检查注解中的白名单
        if (!isIpAllowed && !annotationWhitelist.isEmpty()) {
            isIpAllowed = IpWhiteUtil.isIpInWhitelist(clientIp, List.copyOf(annotationWhitelist));
        }

        // 检查配置中的白名单
        if (!isIpAllowed && ipWhiteProperties.getIps() != null && !ipWhiteProperties.getIps().isEmpty()) {
            isIpAllowed = IpWhiteUtil.isIpInWhitelist(clientIp, ipWhiteProperties.getIps());
        }

        if (!isIpAllowed) {
            String methodName = point.getSignature().getName();
            String className = point.getTarget().getClass().getName();
            log.warn("IP访问受限 - 类: {}, 方法: {}, IP: {}", className, methodName, clientIp);
            throw new ServiceException("您的IP地址不在白名单中，禁止访问");
        }
    }
}
