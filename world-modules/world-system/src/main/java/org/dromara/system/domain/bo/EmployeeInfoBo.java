package org.dromara.system.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * OA员工数据业务对象 employee_info
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
//@AutoMapper(target = EmployeeInfo.class, reverseConvertGenerate = false)
public class EmployeeInfoBo extends BaseEntity {

    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ID;

    /**
     * 工号
     */
    @NotBlank(message = "工号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gh;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String xm;

    /**
     * 指纹号/工号
     */
    @NotBlank(message = "指纹号/工号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zwh;

    /**
     * 卡号
     */
    @NotBlank(message = "卡号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String kh;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String xb;

    /**
     * 部门编码
     */
    @NotBlank(message = "部门编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bmbm;

    /**
     * 部门全称
     */
    @NotBlank(message = "部门全称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sjbmbm;

    /**
     * 中心
     */
    @NotBlank(message = "中心不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bmqc;

    /**
     * 部门
     */
    @NotBlank(message = "部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bm;

    /**
     * 组别
     */
    @NotBlank(message = "组别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zb;

    /**
     * 部门简称
     */
    @NotBlank(message = "部门简称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bmjc;

    /**
     * 所属小组
     */
    @NotBlank(message = "所属小组不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ssxz;

    /**
     * 一级审批者
     */
    @NotBlank(message = "一级审批者不能为空", groups = { AddGroup.class, EditGroup.class })
    private String yjspz;

    /**
     * 二级审批者
     */
    @NotBlank(message = "二级审批者不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ejspz;

    /**
     * 三级审批者
     */
    @NotBlank(message = "三级审批者不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sjspz;

    /**
     * 岗位编码
     */
    @NotBlank(message = "岗位编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String swbm;

    /**
     * 岗位
     */
    @NotBlank(message = "岗位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gw;

    /**
     * 兼任部门岗位
     */
    @NotBlank(message = "兼任部门岗位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String jrbmgw;

    /**
     * 职级代码编码
     */
    @NotBlank(message = "职级代码编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zjdmbm;

    /**
     * 职级
     */
    @NotBlank(message = "职级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zj;

    /**
     * 职称
     */
    @NotBlank(message = "职称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zc;

    /**
     * 员工状态
     */
    @NotBlank(message = "员工状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ygzt;

    /**
     * 聘用日期
     */
    @NotNull(message = "聘用日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date pyrq;

    /**
     * 离职日期
     */
    @NotNull(message = "离职日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lzrq;

    /**
     * 试用期开始日期
     */
    @NotNull(message = "试用期开始日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date syq;

    /**
     * 转正日期
     */
    @NotNull(message = "转正日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date zzrq;

    /**
     * 是否转正
     */
    @NotNull(message = "是否转正不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sfzz;

    /**
     * 工龄（可能需要根据实际需求选择更合适的类型，如YEAR等）
     */
    @NotNull(message = "工龄（可能需要根据实际需求选择更合适的类型，如YEAR等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long gl;

    /**
     * 最高学历
     */
    @NotBlank(message = "最高学历不能为空", groups = { AddGroup.class, EditGroup.class })
    private String zgxl;

    /**
     * 学习专业
     */
    @NotBlank(message = "学习专业不能为空", groups = { AddGroup.class, EditGroup.class })
    private String xxzy;

    /**
     * 毕业学校
     */
    @NotBlank(message = "毕业学校不能为空", groups = { AddGroup.class, EditGroup.class })
    private String byxx;

    /**
     * 英语水平（如CET-4, CET-6, TOEFL等）
     */
    @NotBlank(message = "英语水平（如CET-4, CET-6, TOEFL等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String yysp;

    /**
     * 办公电话
     */
    @NotBlank(message = "办公电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bgdh;

    /**
     * 手机
     */
    @NotBlank(message = "手机不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sj;

    /**
     * 公司邮件
     */
    @NotBlank(message = "公司邮件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gsyj;

    /**
     * 虚拟组织代号
     */
    @NotBlank(message = "虚拟组织代号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String xnzzdh;

    /**
     * 虚拟组织名称
     */
    @NotBlank(message = "虚拟组织名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String xnzzmc;


}
