package org.dromara.system.domain.bo;

import org.dromara.system.domain.SysAutoCodeRule;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 编码生成规则业务对象 sys_auto_code_rule
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysAutoCodeRule.class, reverseConvertGenerate = false)
public class SysAutoCodeRuleBo extends BaseEntity {

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = { EditGroup.class })
    private Long ruleId;

    /**
     * 规则编码
     */
    @NotBlank(message = "规则编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleCode;

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleName;

    /**
     * 描述
     */
    private String ruleDesc;

    /**
     * 最大长度
     */
    private Long maxLength;

    /**
     * 是否补齐
     */
    private String isPadded;

    /**
     * 补齐字符
     */
    private String paddedChar;

    /**
     * 补齐方式
     */
    private String paddedMethod;

    /**
     * 启用状态
     */
    @NotBlank(message = "启用状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String enableFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
