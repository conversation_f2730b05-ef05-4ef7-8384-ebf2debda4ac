package org.dromara.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.system.domain.SyncTimestamp;

import java.util.Date;

/**
 * 任务增量同步日期记录业务对象 sync_timestamp
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SyncTimestamp.class, reverseConvertGenerate = false)
public class SyncTimestampBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastSyncTime;


}
