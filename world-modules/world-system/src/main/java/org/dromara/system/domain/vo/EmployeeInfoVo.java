package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * OA员工数据视图对象 employee_info
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@ExcelIgnoreUnannotated
//@AutoMapper(target = EmployeeInfo.class)
public class EmployeeInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @ExcelProperty(value = "员工ID")
    private Long ID;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号")
    private String GH;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String XM;

    /**
     * 指纹号/工号
     */
    @ExcelProperty(value = "指纹号/工号")
    private String ZWH;

    /**
     * 卡号
     */
    @ExcelProperty(value = "卡号")
    private String KH;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    private String XB;

    /**
     * 部门编码
     */
    @ExcelProperty(value = "部门编码")
    private String BMBM;

    /**
     * 部门全称
     */
    @ExcelProperty(value = "部门全称")
    private String SJBMBM;

    /**
     * 中心
     */
    @ExcelProperty(value = "中心")
    private String BMQC;

    /**
     * 部门
     */
    @ExcelProperty(value = "部门")
    private String BM;

    /**
     * 组别
     */
    @ExcelProperty(value = "组别")
    private String ZB;

    /**
     * 部门简称
     */
    @ExcelProperty(value = "部门简称")
    private String BMJC;

    /**
     * 所属小组
     */
    @ExcelProperty(value = "所属小组")
    private String SSXZ;

    /**
     * 一级审批者
     */
    @ExcelProperty(value = "一级审批者")
    private String YJSPZ;

    /**
     * 二级审批者
     */
    @ExcelProperty(value = "二级审批者")
    private String EJSPZ;

    /**
     * 三级审批者
     */
    @ExcelProperty(value = "三级审批者")
    private String SJSPZ;

    /**
     * 职级
     */
    @ExcelProperty(value = "职级")
    private String ZJ;

    /**
     * 职称
     */
    @ExcelProperty(value = "职称")
    private String ZC;

    /**
     * 员工状态
     */
    @ExcelProperty(value = "员工状态")
    private String YGZT;

    /**
     * 聘用日期
     */
    @ExcelProperty(value = "聘用日期")
    private Date PYRQ;

    /**
     * 离职日期
     */
    @ExcelProperty(value = "离职日期")
    private Date LZRQ;

    /**
     * 试用期开始日期
     */
    @ExcelProperty(value = "试用期开始日期")
    private Date SYQ;

    /**
     * 转正日期
     */
    @ExcelProperty(value = "转正日期")
    private Date ZZRQ;

    /**
     * 是否转正
     */
    @ExcelProperty(value = "是否转正")
    private Long SFZZ;

    /**
     * 工龄（可能需要根据实际需求选择更合适的类型，如YEAR等）
     */
    @ExcelProperty(value = "工龄", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=能需要根据实际需求选择更合适的类型，如YEAR等")
    private Long GL;

    /**
     * 最高学历
     */
    @ExcelProperty(value = "最高学历")
    private String ZGXL;

    /**
     * 学习专业
     */
    @ExcelProperty(value = "学习专业")
    private String XXZY;

    /**
     * 毕业学校
     */
    @ExcelProperty(value = "毕业学校")
    private String BYXX;

    /**
     * 英语水平（如CET-4, CET-6, TOEFL等）
     */
    @ExcelProperty(value = "英语水平", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=CET-4,,C=ET-6,,T=OEFL等")
    private String YYSP;

    /**
     * 办公电话
     */
    @ExcelProperty(value = "办公电话")
    private String BGDH;

    /**
     * 手机
     */
    @ExcelProperty(value = "手机")
    private String SJ;

    /**
     * 公司邮件
     */
    @ExcelProperty(value = "公司邮件")
    private String GSYJ;

    /**
     * 虚拟组织代号
     */
    @ExcelProperty(value = "虚拟组织代号")
    private String XNZZDH;

    /**
     * 虚拟组织名称
     */
    @ExcelProperty(value = "虚拟组织名称")
    private String XNZZMC;


}
