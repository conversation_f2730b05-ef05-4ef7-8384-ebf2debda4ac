package org.dromara.system.domain.bo;

import org.dromara.system.domain.SysAutoCodeResult;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 编码生成记录业务对象 sys_auto_code_result
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysAutoCodeResult.class, reverseConvertGenerate = false)
public class SysAutoCodeResultBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long codeId;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ruleId;

    /**
     * 生成日期时间
     */
    @NotBlank(message = "生成日期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String genDate;

    /**
     * 最后产生的序号
     */
    private Integer genIndex;

    /**
     * 最后产生的值
     */
    private String lastResult;

    /**
     * 最后产生的流水号
     */
    private Integer lastSerialNo;

    /**
     * 最后传入的参数
     */
    private String lastInputChar;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
