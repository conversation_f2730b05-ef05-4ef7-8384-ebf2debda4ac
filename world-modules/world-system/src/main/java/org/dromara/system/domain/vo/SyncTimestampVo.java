package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.SyncTimestamp;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 任务增量同步日期记录视图对象 sync_timestamp
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SyncTimestamp.class)
public class SyncTimestampVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date lastSyncTime;


}
