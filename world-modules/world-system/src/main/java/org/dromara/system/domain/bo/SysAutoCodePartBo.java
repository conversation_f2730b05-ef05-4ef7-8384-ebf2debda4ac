package org.dromara.system.domain.bo;

import org.dromara.system.domain.SysAutoCodePart;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 编码生成规则组成业务对象 sys_auto_code_part
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysAutoCodePart.class, reverseConvertGenerate = false)
public class SysAutoCodePartBo extends BaseEntity {

    /**
     * 分段ID
     */
    @NotNull(message = "分段ID不能为空", groups = { EditGroup.class })
    private Long partId;

    /**
     * 规则ID
     */
    @NotNull(message = "规则ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ruleId;

    /**
     * 分段序号
     */
    @NotNull(message = "分段序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long partIndex;

    /**
     * 分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号
     */
    @NotBlank(message = "分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String partType;

    /**
     * 分段编号
     */
    private String partCode;

    /**
     * 分段名称
     */
    private String partName;

    /**
     * 分段长度
     */
    @NotNull(message = "分段长度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long partLength;

    /**
     *
     */
    private String dateFormat;

    /**
     * 输入字符
     */
    private String inputCharacter;

    /**
     * 固定字符
     */
    private String fixCharacter;

    /**
     * 流水号起始值
     */
    private Long seriaStartNo;

    /**
     * 流水号步长
     */
    private Long seriaStep;

    /**
     * 流水号当前值
     */
    private Long seriaNowNo;

    /**
     * 流水号是否循环
     */
    private String cycleFlag;

    /**
     * 循环方式，YEAR：按年，MONTH：按月，DAY：按天，HOUR：按小时，MINITE：按分钟，OTHER：按传入字符变
     */
    private String cycleMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
