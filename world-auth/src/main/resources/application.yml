# Tomcat
server:
  port: 9210

# Spring
spring:
  application:
    # 应用名称
    name: world-auth
  profiles:
    # 环境配置
    active: @profiles.active@

--- # ldap 配置
spring:
  # LADP配置
  ldap:
    urls: ldaps://192.168.123.2:636
    username: <EMAIL>
    password: World@123456789
    base: dc=world,dc=com,dc=cn
--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server@
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        # 注册组
        group: @nacos.discovery.group@
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: @nacos.config.group@
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
