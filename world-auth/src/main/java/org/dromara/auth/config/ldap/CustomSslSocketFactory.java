package org.dromara.auth.config.ldap;

import lombok.extern.slf4j.Slf4j;

import javax.net.SocketFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.UnknownHostException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

/**
 * 自定义 SSL Socket Factory，用于绕过 SSL/TLS 证书验证。
 * 注意：出于安全考虑，此类应仅在测试环境中使用。
 * <AUTHOR>
 */
@Slf4j
public class CustomSslSocketFactory extends SSLSocketFactory {

    private static final SSLSocketFactory socketFactory;

    static {
        try {
            SSLContext ctx = SSLContext.getInstance("TLS");
            ctx.init(null, new TrustManager[]{new DummyTrustManager()}, new java.security.SecureRandom());
            socketFactory = ctx.getSocketFactory();
            log.info("CustomSslSocketFactory initialized successfully.");
        } catch (Exception ex) {
            throw new RuntimeException("Failed to initialize CustomSslSocketFactory", ex);
        }
    }


    public static SocketFactory getDefault() {
        return new CustomSslSocketFactory();
    }

    @Override
    public String[] getDefaultCipherSuites() {
        return socketFactory.getDefaultCipherSuites();
    }

    @Override
    public String[] getSupportedCipherSuites() {
        return socketFactory.getSupportedCipherSuites();
    }

    @Override
    public Socket createSocket(Socket socket, String host, int port, boolean autoClose) throws IOException {
//        log.info("Creating socket with host: " + host + ", port: " + port);
        return socketFactory.createSocket(socket, host, port, autoClose);
    }
    @Override
    public Socket createSocket(String host, int port) throws IOException, UnknownHostException {
//        log.info("Creating socket with host: " + host + ", port: " + port);
        return socketFactory.createSocket(host, port);
    }

    @Override
    public Socket createSocket(String host, int port, InetAddress localAddr, int localPort) throws IOException, UnknownHostException {
        log.info("Creating socket with host: " + host + ", port: " + port + ", localAddr: " + localAddr + ", localPort: " + localPort);
        return socketFactory.createSocket(host, port, localAddr, localPort);
    }

    @Override
    public Socket createSocket(InetAddress host, int port) throws IOException {
//        log.info("Creating socket with host: " + host + ", port: " + port);
        return socketFactory.createSocket(host, port);
    }

    @Override
    public Socket createSocket(InetAddress host, int port, InetAddress localAddr, int localPort) throws IOException {
//        log.info("Creating socket with host: " + host + ", port: " + port + ", localAddr: " + localAddr + ", localPort: " + localPort);
        return socketFactory.createSocket(host, port, localAddr, localPort);
    }


    /**
     * 绕过证书校验的 TrustManager。
     */
    public static class DummyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            // 故意留空，以绕过证书校验
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            // 故意留空，以绕过证书校验
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }
}
