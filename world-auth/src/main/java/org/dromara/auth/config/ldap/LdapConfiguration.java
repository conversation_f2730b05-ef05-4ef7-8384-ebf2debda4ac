package org.dromara.auth.config.ldap;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.ldap.LdapProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import javax.naming.Context;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/02/01
 */
@Configuration
@Slf4j
public class LdapConfiguration {

    @Resource
    LdapProperties ldapProperties;

    private LdapTemplate ldapTemplate;


    /**
     * 继承LdapContextSource重写getAnonymousEnv方法来加载，
     * 使连接ldap时用SSL连接（由于修改AD密码时必须使用SSL连接）
     */

    @Bean
    public LdapContextSource contextSource() {
        System.setProperty("com.sun.jndi.ldap.object.disableEndpointIdentification", "true");
        LdapContextSource ldapContextSource = new LdapContextSource();
        ldapContextSource.setBase(ldapProperties.getBase());
        ldapContextSource.setUrls(ldapProperties.getUrls());
        ldapContextSource.setUserDn(ldapProperties.getUsername());
        ldapContextSource.setPassword(ldapProperties.getPassword());
        ldapContextSource.setReferral("follow");
        //  开启缓存
        ldapContextSource.setCacheEnvironmentProperties(true);
        ldapContextSource.setPooled(true);


        Map<String, Object> config = new HashMap<>(8);
        config.put(Context.SECURITY_PROTOCOL, "ssl");
        config.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        //  解决乱码
        config.put("java.naming.ldap.attributes.binary", "objectGUID");
        // 跳过ssl
        config.put("java.naming.ldap.factory.socket", CustomSslSocketFactory.class.getName());
        ldapContextSource.setBaseEnvironmentProperties(config);
        ldapContextSource.afterPropertiesSet();


        return ldapContextSource;
    }

    @Bean
    public LdapTemplate ldapTemplate(LdapContextSource contextSource) {
        if (Objects.isNull(contextSource)) {
            throw new RuntimeException("ldap contextSource error");
        }
        if (null == ldapTemplate) {
            ldapTemplate = new LdapTemplate(contextSource);
        }
        return ldapTemplate;
    }
}
