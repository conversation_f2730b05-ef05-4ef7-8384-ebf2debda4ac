-- 天心天思数据同步相关表结构 - SQL Server版本
-- 创建时间: 2025-01-27
-- 说明: 用于存储天心天思API对接的同步日志和配置信息
-- 数据库: vn_scan

-- 使用数据库
USE vn_scan;
GO

-- 同步日志表
CREATE TABLE [dbo].[t_sync_log] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,
    [module] NVARCHAR(50) NOT NULL,
    [data_type] NVARCHAR(50) NOT NULL,
    [data_id] NVARCHAR(100) NOT NULL,
    [sync_status] TINYINT NOT NULL,
    [sync_time] DATETIME NOT NULL,
    [error_message] NVARCHAR(MAX) NULL,
    [request_data] NVARCHAR(MAX) NULL,
    [response_data] NVARCHAR(MAX) NULL,
    [response_time] BIGINT NULL,
    [retry_count] INT DEFAULT 0,
    [remark] NVARCHAR(500) NULL,
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                -- 租户ID
    [create_dept] BIGINT NULL,                   -- 创建部门
    [create_by] BIGINT NULL,                     -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),    -- 创建时间
    [update_by] BIGINT NULL,                     -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),    -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                -- 删除标志（0代表存在 1代表删除）
    CONSTRAINT [PK_t_sync_log] PRIMARY KEY ([id])
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'天心天思数据同步日志表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N't_sync_log';
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'同步模块', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'module';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'数据类型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'data_type';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'数据ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'data_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'同步状态: 0-失败 1-成功', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'sync_status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'同步时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'sync_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'错误信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'error_message';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'请求数据', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'request_data';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'响应数据', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'response_data';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'响应时间（毫秒）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'response_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'重试次数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'retry_count';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'备注信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'remark';
-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_log', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 同步配置表
CREATE TABLE [dbo].[t_sync_config] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,
    [module] NVARCHAR(50) NOT NULL,
    [config_key] NVARCHAR(100) NOT NULL,
    [config_value] NVARCHAR(MAX) NULL,
    [description] NVARCHAR(500) NULL,
    [enabled] TINYINT DEFAULT 1,
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                -- 租户ID
    [create_dept] BIGINT NULL,                   -- 创建部门
    [create_by] BIGINT NULL,                     -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),    -- 创建时间
    [update_by] BIGINT NULL,                     -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),    -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                -- 删除标志（0代表存在 1代表删除）
    CONSTRAINT [PK_t_sync_config] PRIMARY KEY ([id])
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'天心天思数据同步配置表',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N't_sync_config';
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'模块名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'module';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'配置键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'config_key';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'配置值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'config_value';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'配置说明', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'description';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否启用: 1-启用 0-禁用', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'enabled';
-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N't_sync_config', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建索引
CREATE INDEX [IX_t_sync_log_module_type] ON [dbo].[t_sync_log] ([module], [data_type]);
CREATE INDEX [IX_t_sync_log_sync_time] ON [dbo].[t_sync_log] ([sync_time]);
CREATE INDEX [IX_t_sync_log_data_id] ON [dbo].[t_sync_log] ([data_id]);
CREATE INDEX [IX_t_sync_log_sync_status] ON [dbo].[t_sync_log] ([sync_status]);
CREATE INDEX [IX_t_sync_log_composite] ON [dbo].[t_sync_log] ([module], [data_type], [sync_status], [sync_time]);
CREATE INDEX [IX_t_sync_log_cleanup] ON [dbo].[t_sync_log] ([sync_time], [sync_status]);

-- 创建唯一索引
CREATE UNIQUE INDEX [IX_t_sync_config_module_key] ON [dbo].[t_sync_config] ([module], [config_key]);
GO

-- 插入默认配置数据
INSERT INTO [dbo].[t_sync_config] ([module], [config_key], [config_value], [description], [enabled]) VALUES
('SYSTEM', 'sync.enabled', 'true', '是否启用数据同步', 1),
('SYSTEM', 'sync.batch.size', '100', '批量同步大小', 1),
('SYSTEM', 'sync.retry.times', '3', '重试次数', 1),
('SYSTEM', 'sync.retry.interval', '1000', '重试间隔（毫秒）', 1),
('SYSTEM', 'sync.timeout', '30000', '同步超时时间（毫秒）', 1),
('PRODUCT', 'sync.enabled', 'true', '商品同步是否启用', 1),
('PRODUCT', 'sync.schedule', '0 0 2 * * ?', '商品同步定时任务', 1),
('CUSTOMER', 'sync.enabled', 'true', '客户同步是否启用', 1),
('CUSTOMER', 'sync.schedule', '0 0 2 * * ?', '客户同步定时任务', 1),
('SUPPLIER', 'sync.enabled', 'true', '供应商同步是否启用', 1),
('SUPPLIER', 'sync.schedule', '0 0 2 * * ?', '供应商同步定时任务', 1),
('INVENTORY', 'sync.enabled', 'true', '库存同步是否启用', 1),
('INVENTORY', 'sync.schedule', '0 */5 * * * ?', '库存同步定时任务', 1),
('ORDER', 'sync.enabled', 'true', '订单同步是否启用', 1),
('ORDER', 'sync.schedule', '0 */5 * * * ?', '订单同步定时任务', 1);
GO

-- 创建视图用于统计查询
CREATE VIEW [dbo].[v_sync_statistics] AS
SELECT
    [module],
    [data_type],
    COUNT(*) as [total_count],
    SUM(CASE WHEN [sync_status] = 1 THEN 1 ELSE 0 END) as [success_count],
    SUM(CASE WHEN [sync_status] = 0 THEN 1 ELSE 0 END) as [fail_count],
    ROUND(SUM(CASE WHEN [sync_status] = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as [success_rate],
    AVG([response_time]) as [avg_response_time],
    MAX([sync_time]) as [last_sync_time]
FROM [dbo].[t_sync_log]
GROUP BY [module], [data_type];
GO

-- 创建存储过程用于清理过期日志
CREATE PROCEDURE [dbo].[sp_clean_expired_sync_logs]
    @days INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @affected_rows INT = 0;

    -- 删除指定天数前的日志
    DELETE FROM [dbo].[t_sync_log]
    WHERE [sync_time] < DATEADD(DAY, -@days, GETDATE());

    SET @affected_rows = @@ROWCOUNT;

    -- 记录清理结果
    INSERT INTO [dbo].[t_sync_log] ([module], [data_type], [data_id], [sync_status], [sync_time], [remark])
    VALUES ('SYSTEM', 'CLEANUP', 'EXPIRED_LOGS', 1, GETDATE(), '清理了 ' + CAST(@affected_rows AS NVARCHAR(10)) + ' 条过期日志');

    SELECT '成功清理 ' + CAST(@affected_rows AS NVARCHAR(10)) + ' 条过期日志' as result;
END;
GO

-- 创建函数用于获取同步状态描述
CREATE FUNCTION [dbo].[fn_get_sync_status_name](@status TINYINT)
RETURNS NVARCHAR(20)
AS
BEGIN
    DECLARE @result NVARCHAR(20);

    IF @status = 0
        SET @result = '失败';
    ELSE IF @status = 1
        SET @result = '成功';
    ELSE
        SET @result = '未知';

    RETURN @result;
END;
GO




-- 添加视图注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'同步统计视图',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'v_sync_statistics';
GO

-- 添加存储过程注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'清理过期同步日志存储过程',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'PROCEDURE', @level1name = N'sp_clean_expired_sync_logs';
GO

-- 添加函数注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'获取同步状态描述函数',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'FUNCTION', @level1name = N'fn_get_sync_status_name';
GO

-- =============================================
-- 订单信息总表 (Master) - 越南ERP系统
-- 创建时间: 2025-01-09
-- 描述: 订单信息主表，包含订单的完整信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Master]') AND type in (N'U'))
    DROP TABLE [dbo].[Master]
GO

-- 创建订单信息总表
CREATE TABLE [dbo].[Master] (
    -- 主键字段
    [id] BIGINT IDENTITY(1,1) NOT NULL,  -- 主键，自增

    -- 基础信息字段
    [sta] NVARCHAR(30) NULL,                     -- 状态
    [rem] NVARCHAR(100) NULL,                    -- 备注

    -- 订单日期信息
    [order_date] SMALLDATETIME NULL,              -- 接单日期
    [customer_req_delivery_date] SMALLDATETIME NULL, -- 客户要求交期
    [promised_delivery_date] SMALLDATETIME NULL,   -- 承诺交期
    [next_plan_date] SMALLDATETIME NULL,           -- 下计划日期
    [required_completion_date] SMALLDATETIME NULL, -- 要求完成日期
    [last_process_time] SMALLDATETIME NULL,        -- 末工序时间
    [warehouse_entry_date] SMALLDATETIME NULL,     -- 入仓日期
    [actual_delivery_date] SMALLDATETIME NULL,     -- 实际交期

    -- 客户和项目信息
    [customer_code] NVARCHAR(5) NULL,             -- 客户代码
    [project_no] NVARCHAR(20) NULL,               -- 项目号
    [project_manager] NVARCHAR(20) NULL,          -- 项目经理
    [customer_po_no] NVARCHAR(50) NULL,            -- 客户PO号
    [po_itm] NVARCHAR(10) NULL,                  -- PO_ITM

    -- 订单编号信息
    [so_no] NVARCHAR(20) NULL,                   -- SO号
    [so_itm] NVARCHAR(10) NULL,                  -- SO项次
    [mo_no] NVARCHAR(20) NULL,                   -- MO_NO (主键不重复)
    [parent_order_no] NVARCHAR(20) NULL,           -- 上层订单号

    -- 产品信息
    [part_classification] NVARCHAR(1) NULL,       -- 零件分类
    [prd_no] NVARCHAR(20) NULL,                  -- 品号
    [dwg_no] NVARCHAR(100) NULL,                 -- 图号
    [version] NVARCHAR(15) NULL,                 -- 版本
    [customer_product_no] NVARCHAR(50) NULL,       -- 客户产品料号
    [customer_product_name] NVARCHAR(50) NULL,     -- 客户产品名称
    [product_description] NVARCHAR(50) NULL,      -- 产品描述

    -- 数量信息
    [order_quantity] FLOAT NULL DEFAULT 0,        -- 订单数量
    [production_quantity] FLOAT NULL DEFAULT 0,   -- 生产数量
    [process_quantity] FLOAT NULL DEFAULT 0,      -- 工艺数量
    [scrap_quantity] FLOAT NULL DEFAULT 0,        -- 报废数量
    [in_warehouse_count] FLOAT NULL DEFAULT 0,     -- 已入仓数
    [outstanding_warehouse_count] FLOAT NULL DEFAULT 0, -- 欠入仓数
    [delivery_quantity] FLOAT NULL DEFAULT 0,     -- 交货数量
    [outstanding_shipment_quantity] FLOAT NULL DEFAULT 0, -- 欠出货数量
    [single_piece_net_weight] FLOAT NULL DEFAULT 0, -- 单件净重

    -- 单位和分类信息
    [unit] NVARCHAR(10) NULL,                    -- 单位
    [industry] NVARCHAR(10) NULL,                -- 行业
    [order_category] NVARCHAR(20) NULL,           -- 订单类别
    [plan_type] NVARCHAR(10) NULL,                -- 计划类型

    -- 人员信息
    [order_placer] NVARCHAR(20) NULL,             -- 下单员
    [follow_up_staff] NVARCHAR(20) NULL,           -- 跟单员
    [bom_manager] NVARCHAR(20) NULL,              -- BOM负责人
    [process_compiler] NVARCHAR(50) NULL,         -- 工艺编制人

    -- 标识字段
    [merge_flag] NVARCHAR(1) NULL,                -- 合单标识
    [urgent_flag] NVARCHAR(1) NULL,               -- 急单标识
    [sample_flag] NVARCHAR(1) NULL,               -- 打样标识
    [has_standard_parts] NVARCHAR(1) NULL,         -- 是否有标准件
    [is_material_purchased] NVARCHAR(1) NULL,      -- 是否买料
    [is_electroplated] NVARCHAR(1) NULL,          -- 是否电镀

    -- 工艺和材料信息
    [action] NVARCHAR(50) NULL,                  -- 动作
    [current_process] NVARCHAR(30) NULL,          -- 当前工序
    [processing_material] NVARCHAR(600) NULL,     -- 加工材料
    [required_material] NVARCHAR(50) NULL,        -- 要求材料
    [quoted_material] NVARCHAR(50) NULL,          -- 报价材料
    [drawing_difficulty_level] NVARCHAR(10) NULL,  -- 图纸难度等级
    [process_difficulty_level] NVARCHAR(10) NULL,  -- 工艺难度等级
    [process_version] NVARCHAR(10) NULL,          -- 工艺版本

    -- 部门和成本信息
    [completion_department] NVARCHAR(50) NULL,    -- 完成部门 (供应商)
    [production_workshop] NVARCHAR(50) NULL,      -- 生产车间 (加工车间)
    [profit_center] NVARCHAR(20) NULL,            -- 利润中心
    [cost_center] NVARCHAR(15) NULL,              -- 成本中心

    -- 其他信息
    [charge_remarks] NVARCHAR(50) NULL,           -- 收费备注
    [close_order_reason] NVARCHAR(50) NULL,        -- 关单原因
    [sta1] INT NULL DEFAULT 0,                   -- 状态1

    -- 同步标记字段 (nchar(1))
    [so_document] NCHAR(1) NULL,                  -- SO单 (同步标记)
    [mo_document] NCHAR(1) NULL,                  -- MO号 (同步标记)
    [pmc_group] NCHAR(1) NULL,                    -- PMC分组 (同步标记)
    [process] NCHAR(1) NULL,                     -- 工艺 (同步标记)
    [production_scheduling] NCHAR(1) NULL,        -- 排产 (同步标记)
    [mrp] NCHAR(1) NULL,                         -- MRP (同步标记)
    [purchase_request] NCHAR(1) NULL,             -- 请购 (同步标记)
    [purchase_order] NCHAR(1) NULL,               -- 下采购单 (同步标记)
    [material_receipt] NCHAR(1) NULL,             -- 材料收货 (同步标记)
    [iqc_inspection] NCHAR(1) NULL,               -- IQC检测 (同步标记)
    [material_return] NCHAR(1) NULL,              -- 材料退货 (同步标记)
    [material_inbound] NCHAR(1) NULL,             -- 材料进仓 (同步标记)
    [subcontracting] NCHAR(1) NULL,              -- 托工 (同步标记)

    -- 同步天心单号字段
    [purchase_request_no] NVARCHAR(20) NULL,       -- 请购单号 (同步天心单号)
    [purchase_order_no] NVARCHAR(20) NULL,         -- 采购单号 (同步天心单号)
    [warehouse_entry_no] NVARCHAR(20) NULL,        -- 入库单号 (同步天心单号)
    [warehouse_exit_no] NVARCHAR(20) NULL,         -- 出库单号 (同步天心单号)
    [sales_order_no] NVARCHAR(20) NULL,            -- 销货单号 (同步天心单号)

    -- 删除标识
    [delete_flag] FLOAT NULL DEFAULT 0,           -- 删除标识 (0-正常;1-删除)

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                -- 租户ID
    [create_dept] BIGINT NULL,                   -- 创建部门
    [create_by] BIGINT NULL,                     -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),    -- 创建时间
    [update_by] BIGINT NULL,                     -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),    -- 更新时间

    -- 主键约束
    CONSTRAINT [PK_Master] PRIMARY KEY CLUSTERED ([id] ASC)
)
GO

-- 创建唯一约束 (mo_no主键不重复)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Master]') AND name = N'UK_Master_mo_no')
    CREATE UNIQUE NONCLUSTERED INDEX [UK_Master_mo_no] ON [dbo].[Master] ([mo_no] ASC)
    WHERE [mo_no] IS NOT NULL
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_Master_customer_code] ON [dbo].[Master] ([customer_code] ASC);
CREATE NONCLUSTERED INDEX [IX_Master_project_no] ON [dbo].[Master] ([project_no] ASC);
CREATE NONCLUSTERED INDEX [IX_Master_so_no] ON [dbo].[Master] ([so_no] ASC);
CREATE NONCLUSTERED INDEX [IX_Master_prd_no] ON [dbo].[Master] ([prd_no] ASC);
CREATE NONCLUSTERED INDEX [IX_Master_order_date] ON [dbo].[Master] ([order_date] ASC);
CREATE NONCLUSTERED INDEX [IX_Master_sta] ON [dbo].[Master] ([sta] ASC);
CREATE NONCLUSTERED INDEX [IX_Master_delete_flag] ON [dbo].[Master] ([delete_flag] ASC);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'订单信息总表 - 存储订单的完整信息，包括客户信息、产品信息、数量信息、工艺信息等',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'Master'
GO

-- 添加主要字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键，自增ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'订单状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'sta';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'备注信息', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'rem';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'接单日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'order_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'customer_code';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'项目号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'project_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'SO号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'so_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MO_NO (主键不重复)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'mo_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'品号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'prd_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'订单数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'order_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标识 (0-正常;1-删除)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'delete_flag';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'update_time';

-- 添加缺失的字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户要求交期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'customer_req_delivery_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'承诺交期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'promised_delivery_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'下计划日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'next_plan_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'要求完成日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'required_completion_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'末工序时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'last_process_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'入仓日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'warehouse_entry_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'实际交期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'actual_delivery_date';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'项目经理', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'project_manager';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户PO号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'customer_po_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PO_ITM', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'po_itm';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'SO项次', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'so_itm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'上层订单号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'parent_order_no';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'零件分类', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'part_classification';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'图号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'dwg_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'版本', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'version';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户产品料号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'customer_product_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户产品名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'customer_product_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'产品描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'product_description';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'生产数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'production_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'process_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'报废数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'scrap_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'已入仓数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'in_warehouse_count';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'欠入仓数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'outstanding_warehouse_count';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'交货数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'delivery_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'欠出货数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'outstanding_shipment_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'单件净重', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'single_piece_net_weight';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'单位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'unit';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'行业', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'industry';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'订单类别', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'order_category';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'计划类型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'plan_type';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'下单员', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'order_placer';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'跟单员', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'follow_up_staff';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'BOM负责人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'bom_manager';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺编制人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'process_compiler';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'合单标识', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'merge_flag';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'急单标识', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'urgent_flag';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'打样标识', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'sample_flag';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否有标准件', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'has_standard_parts';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否买料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'is_material_purchased';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否电镀', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'is_electroplated';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'动作', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'action';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'当前工序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'current_process';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'加工材料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'processing_material';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'要求材料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'required_material';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'报价材料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'quoted_material';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'图纸难度等级', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'drawing_difficulty_level';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺难度等级', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'process_difficulty_level';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺版本', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'process_version';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'完成部门 (供应商)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'completion_department';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'生产车间 (加工车间)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'production_workshop';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'利润中心', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'profit_center';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'成本中心', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'cost_center';

EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'收费备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'charge_remarks';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'关单原因', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'close_order_reason';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'状态1', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'sta1';

-- 同步标记字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'SO单 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'so_document';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MO号 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'mo_document';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PMC分组 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'pmc_group';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'process';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'排产 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'production_scheduling';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MRP (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'mrp';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'请购 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'purchase_request';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'下采购单 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'purchase_order';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'材料收货 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'material_receipt';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'IQC检测 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'iqc_inspection';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'材料退货 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'material_return';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'材料进仓 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'material_inbound';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'托工 (同步标记)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'subcontracting';

-- 同步天心单号字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'请购单号 (同步天心单号)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'purchase_request_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'采购单号 (同步天心单号)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'purchase_order_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'入库单号 (同步天心单号)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'warehouse_entry_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'出库单号 (同步天心单号)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'warehouse_exit_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'销货单号 (同步天心单号)', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Master', @level2type = N'COLUMN', @level2name = N'sales_order_no';
GO

PRINT '订单信息总表 (Master) 创建完成！'
PRINT '表包含 100+ 个字段，涵盖订单的完整信息和标准系统字段'
PRINT '已创建主键约束、唯一约束和常用索引'
PRINT '已添加表和所有字段的完整注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time'
GO

-- =============================================
-- 排产信息表 (product_schedule_info) - 越南ERP系统
-- 创建时间: 2025-01-09
-- 描述: 存储生产排产的详细信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[product_schedule_info]') AND type in (N'U'))
    DROP TABLE [dbo].[product_schedule_info]
GO

-- 创建排产信息表
CREATE TABLE [dbo].[product_schedule_info] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,  -- 主键
    [aps_no] NVARCHAR(20) NULL,                  -- 排产单号
    [itm] INT NULL,                              -- 排产项次
    [mo_no] NVARCHAR(20) NULL,                   -- MO号
    [zc_itm] INT NULL,                           -- 工序号
    [zc_nm] NVARCHAR(20) NULL,                   -- 工序名称
    [zc_no] NVARCHAR(10) NULL,                   -- 制程代号
    [zgs] NUMERIC(22, 4) NULL,                   -- 总工时
    [dep] NVARCHAR(12) NULL,                     -- 机台
    [zgs_erp] NUMERIC(22, 4) NULL,               -- ERP总工时
    [waittime] NUMERIC(22, 4) NULL,              -- 调试时间
    [kg_dd] DATETIME NULL,                       -- 开工时间
    [b_dd] DATETIME NULL,                        -- 排产开始时间
    [e_dd] DATETIME NULL,                        -- 排产结束时间
    [jts] INT NULL DEFAULT 0,                    -- 机台数
    [cus_no] NVARCHAR(12) NULL,                  -- 托工厂商

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                -- 租户ID
    [create_dept] BIGINT NULL,                   -- 创建部门
    [create_by] BIGINT NULL,                     -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),    -- 创建时间
    [update_by] BIGINT NULL,                     -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),    -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                -- 删除标志（0代表存在 1代表删除）

    CONSTRAINT [PK_product_schedule_info] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'排产信息表 - 存储生产排产的详细信息，包括工序、工时、机台等信息',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'product_schedule_info'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'排产单号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'aps_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'排产项次', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'itm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MO号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'mo_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工序号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'zc_itm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工序名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'zc_nm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'制程代号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'zc_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'总工时', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'zgs';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'机台', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'dep';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'ERP总工时', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'zgs_erp';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'调试时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'waittime';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'开工时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'kg_dd';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'排产开始时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'b_dd';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'排产结束时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'e_dd';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'机台数', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'jts';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'托工厂商', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'cus_no';

-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志（0代表存在 1代表删除）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'product_schedule_info', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_product_schedule_info_aps_no] ON [dbo].[product_schedule_info] ([aps_no] ASC);
CREATE NONCLUSTERED INDEX [IX_product_schedule_info_mo_no] ON [dbo].[product_schedule_info] ([mo_no] ASC);
CREATE NONCLUSTERED INDEX [IX_product_schedule_info_zc_no] ON [dbo].[product_schedule_info] ([zc_no] ASC);
CREATE NONCLUSTERED INDEX [IX_product_schedule_info_b_dd] ON [dbo].[product_schedule_info] ([b_dd] ASC);
CREATE NONCLUSTERED INDEX [IX_product_schedule_info_e_dd] ON [dbo].[product_schedule_info] ([e_dd] ASC);
CREATE NONCLUSTERED INDEX [IX_product_schedule_info_del_flag] ON [dbo].[product_schedule_info] ([del_flag] ASC);
GO


PRINT '排产信息表 (product_schedule_info) 创建完成！'
PRINT '表包含 23 个字段，涵盖排产的详细信息和标准系统字段'
PRINT '已创建主键约束、索引和字段注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time, deleted'
GO

-- =============================================
-- 问图记录表 (query_drawing_record) - 越南ERP系统
-- 创建时间: 2025-01-09
-- 描述: 存储问图记录的详细信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[query_drawing_record]') AND type in (N'U'))
    DROP TABLE [dbo].[query_drawing_record]
GO

-- 创建问图记录表
CREATE TABLE [dbo].[query_drawing_record] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,                 -- 主键
    [status] NVARCHAR(50) NULL,                      -- 状态
    [deadline] SMALLDATETIME NULL,                   -- 截止日期
    [mo_no] NVARCHAR(50) NULL,                       -- MO号
    [drawing_no] NVARCHAR(200) NULL,                 -- 图号
    [query_process] NVARCHAR(50) NULL,               -- 问图工序
    [query_person] NVARCHAR(50) NULL,                -- 问图人
    [query_time] SMALLDATETIME NULL,                 -- 问图时间
    [query_content] NVARCHAR(200) NULL,              -- 问图内容
    [engineering_reply_time] SMALLDATETIME NULL,     -- 工程回复时间
    [engineering_responsible] NVARCHAR(50) NULL,     -- 工程责任人
    [engineering_reply_content] NVARCHAR(200) NULL,  -- 工程回复内容
    [engineering_status] NVARCHAR(50) NULL,          -- 工程状态
    [market_reply_time] SMALLDATETIME NULL,          -- 市场回复时间
    [market_responsible] NVARCHAR(50) NULL,          -- 市场责任人
    [market_reply_content] NVARCHAR(200) NULL,       -- 市场回复内容
    [query_type] NVARCHAR(10) NULL,                  -- 问图类型
    [query_job_no] NVARCHAR(5) NULL,                 -- 问图工号
    [process_compiler] NVARCHAR(50) NULL,            -- 工艺编制人
    [customer_code] NVARCHAR(5) NULL,                -- 客户代码
    [query_category] NVARCHAR(30) NULL,              -- 问图类别
    [feedback_type] NVARCHAR(30) NULL,               -- 反馈类型
    [order_placer] NVARCHAR(20) NULL,                -- 下单员
    [order_placer_name] NVARCHAR(20) NULL,           -- 下单员姓名
    [bom_responsible] NVARCHAR(20) NULL,             -- BOM责任人
    [project_manager] NVARCHAR(20) NULL,             -- 项目经理
    [extension_phone] NVARCHAR(20) NULL,             -- 分机电话
    [mobile_phone] NVARCHAR(20) NULL,                -- 手机号码
    [bom_no] NVARCHAR(20) NULL,                      -- BOM_NO
    [profit_center] NVARCHAR(20) NULL,               -- 利润中心
    [stay_duration] FLOAT NULL DEFAULT 0,            -- 停留时长

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                    -- 租户ID
    [create_dept] BIGINT NULL,                       -- 创建部门
    [create_by] BIGINT NULL,                         -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),        -- 创建时间
    [update_by] BIGINT NULL,                         -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),        -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                    -- 删除标志（0代表存在 1代表删除）

    CONSTRAINT [PK_query_drawing_record] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'问图记录表 - 存储问图记录的详细信息，包括问图内容、回复信息等',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'query_drawing_record'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'截止日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'deadline';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MO号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'mo_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'图号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'drawing_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图工序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_process';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_person';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_content';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工程回复时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'engineering_reply_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工程责任人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'engineering_responsible';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工程回复内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'engineering_reply_content';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工程状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'engineering_status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'市场回复时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'market_reply_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'市场责任人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'market_responsible';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'市场回复内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'market_reply_content';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图类型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_type';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图工号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_job_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺编制人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'process_compiler';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'customer_code';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'问图类别', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'query_category';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'反馈类型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'feedback_type';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'下单员', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'order_placer';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'下单员姓名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'order_placer_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'BOM责任人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'bom_responsible';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'项目经理', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'project_manager';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'分机电话', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'extension_phone';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'手机号码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'mobile_phone';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'BOM_NO', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'bom_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'利润中心', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'profit_center';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'停留时长', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'stay_duration';

-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志（0代表存在 1代表删除）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'query_drawing_record', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_query_drawing_record_mo_no] ON [dbo].[query_drawing_record] ([mo_no] ASC);
CREATE NONCLUSTERED INDEX [IX_query_drawing_record_drawing_no] ON [dbo].[query_drawing_record] ([drawing_no] ASC);
CREATE NONCLUSTERED INDEX [IX_query_drawing_record_query_time] ON [dbo].[query_drawing_record] ([query_time] ASC);
CREATE NONCLUSTERED INDEX [IX_query_drawing_record_status] ON [dbo].[query_drawing_record] ([status] ASC);
CREATE NONCLUSTERED INDEX [IX_query_drawing_record_customer_code] ON [dbo].[query_drawing_record] ([customer_code] ASC);
CREATE NONCLUSTERED INDEX [IX_query_drawing_record_del_flag] ON [dbo].[query_drawing_record] ([del_flag] ASC);
GO


PRINT '问图记录表 (query_drawing_record) 创建完成！'
PRINT '表包含 37 个字段，涵盖问图记录的详细信息和标准系统字段'
PRINT '已创建主键约束、索引和字段注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time, deleted'
GO

-- =============================================
-- 扫描记录日志表 (scan_record_log) - 越南ERP系统
-- 创建时间: 2025-01-09
-- 描述: 存储扫描记录的详细信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[scan_record_log]') AND type in (N'U'))
    DROP TABLE [dbo].[scan_record_log]
GO

-- 创建扫描记录日志表
CREATE TABLE [dbo].[scan_record_log] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,      -- 主键
    [mo_no] NVARCHAR(20) NULL,                       -- MO号
    [process_no] INT NULL,                           -- 工序号
    [process_name] NVARCHAR(50) NULL,                -- 工序名称
    [operator] NVARCHAR(50) NULL,                    -- 操作员
    [operator_name] NVARCHAR(50) NULL,               -- 姓名
    [machine_no] NVARCHAR(50) NULL,                  -- 机台号
    [action] NVARCHAR(50) NULL,                      -- 动作
    [start_time] DATETIME NULL,                      -- 开始时间
    [end_time] DATETIME NULL,                        -- 结束时间
    [passed_quantity] INT NULL DEFAULT 0,            -- 通过数量
    [scrapped_quantity] INT NULL DEFAULT 0,          -- 报废数量
    [total_progress] FLOAT NULL DEFAULT 0,           -- 总进度
    [current_progress] FLOAT NULL DEFAULT 0,         -- 当前进度
    [process_man_hours] FLOAT NULL DEFAULT 0,        -- 工艺工时
    [actual_time] FLOAT NULL DEFAULT 0,              -- 实际时间
    [remark] NVARCHAR(200) NULL,                     -- 备注
    [customer_code] NVARCHAR(10) NULL,               -- 客户代码
    [supplier] NVARCHAR(50) NULL,                    -- 供应商
    [storage_location] NVARCHAR(50) NULL,            -- 储位
    [part_no] NVARCHAR(50) NULL,                     -- 品号
    [drawing_no] NVARCHAR(300) NULL,                 -- 图号
    [version] NVARCHAR(20) NULL,                     -- 版本
    [material_description] NVARCHAR(300) NULL,       -- 物料描述
    [customer_product_no] NVARCHAR(50) NULL,         -- 客户产品料号
    [customer_product_name] NVARCHAR(100) NULL,      -- 客户产品名称
    [batch_no] NVARCHAR(50) NULL,                    -- 批号
    [computer_name] NVARCHAR(50) NULL,               -- 电脑名
    [industry] NVARCHAR(20) NULL,                    -- 行业
    [production_schedule_man_hours] FLOAT NULL DEFAULT 0, -- 排产工时
    [creation_time] DATETIME NULL,                   -- 创建时间

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                    -- 租户ID
    [create_dept] BIGINT NULL,                       -- 创建部门
    [create_by] BIGINT NULL,                         -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),        -- 创建时间
    [update_by] BIGINT NULL,                         -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),        -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                    -- 删除标志（0代表存在 1代表删除）

    CONSTRAINT [PK_scan_record_log] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'扫描记录日志表 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'scan_record_log'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MO号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'mo_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工序号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'process_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工序名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'process_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'操作员', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'operator';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'姓名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'operator_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'机台号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'machine_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'动作', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'action';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'开始时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'start_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'结束时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'end_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'通过数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'passed_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'报废数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'scrapped_quantity';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'总进度', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'total_progress';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'当前进度', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'current_progress';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工艺工时', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'process_man_hours';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'实际时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'actual_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'remark';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'customer_code';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'供应商', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'supplier';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'储位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'storage_location';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'品号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'part_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'图号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'drawing_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'版本', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'version';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'物料描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'material_description';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户产品料号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'customer_product_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户产品名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'customer_product_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'批号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'batch_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'电脑名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'computer_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'行业', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'industry';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'排产工时', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'production_schedule_man_hours';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'creation_time';

-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志（0代表存在 1代表删除）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'scan_record_log', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_scan_record_log_mo_no] ON [dbo].[scan_record_log] ([mo_no] ASC);
CREATE NONCLUSTERED INDEX [IX_scan_record_log_process_no] ON [dbo].[scan_record_log] ([process_no] ASC);
CREATE NONCLUSTERED INDEX [IX_scan_record_log_operator] ON [dbo].[scan_record_log] ([operator] ASC);
CREATE NONCLUSTERED INDEX [IX_scan_record_log_start_time] ON [dbo].[scan_record_log] ([start_time] ASC);
CREATE NONCLUSTERED INDEX [IX_scan_record_log_end_time] ON [dbo].[scan_record_log] ([end_time] ASC);
CREATE NONCLUSTERED INDEX [IX_scan_record_log_customer_code] ON [dbo].[scan_record_log] ([customer_code] ASC);
CREATE NONCLUSTERED INDEX [IX_scan_record_log_del_flag] ON [dbo].[scan_record_log] ([del_flag] ASC);
GO


PRINT '扫描记录日志表 (scan_record_log) 创建完成！'
PRINT '表包含 37 个字段，涵盖扫描记录的详细信息和标准系统字段'
PRINT '已创建主键约束、索引和字段注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time, deleted'
GO

-- =============================================
-- 动作信息表 (action_info) - 越南ERP系统
-- 创建时间: 2025-01-09
-- 描述: 存储动作信息的详细信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[action_info]') AND type in (N'U'))
    DROP TABLE [dbo].[action_info]
GO

-- 创建动作信息表
CREATE TABLE [dbo].[action_info] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,                    -- 主键
    [action] NVARCHAR(50) NULL,                         -- 动作
    [department] NVARCHAR(50) NULL,                     -- 部门
    [status] NVARCHAR(1) NULL,                          -- 状态
    [sequence_no] INT NULL,                             -- 序号
    [barcode_no] NVARCHAR(10) NULL,                     -- 条码号
    [is_quantity_controlled] BIT NULL,                  -- 是否控制数量
    [is_passed_quantity_input_required] BIT NULL,       -- 是否输入通过数量
    [is_scrapped_quantity_input_required] BIT NULL,     -- 是否输入报废数量
    [is_previous_process_quantity_carried_over] BIT NULL, -- 是否自带上工序数量
    [allow_skip_process] BIT NULL,                      -- 允许跳工序
    [no_quantity_input_required] BIT NULL,              -- 不用输入数量
    [operator_code] NVARCHAR(5) NULL,                   -- 操作员代码
    [operator_name] NVARCHAR(8) NULL,                   -- 操作人姓名
    [operation_time] SMALLDATETIME NULL,                -- 操作时间

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                       -- 租户ID
    [create_dept] BIGINT NULL,                          -- 创建部门
    [create_by] BIGINT NULL,                            -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),           -- 创建时间
    [update_by] BIGINT NULL,                            -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),           -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                       -- 删除标志（0代表存在 1代表删除）

    CONSTRAINT [PK_action_info] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'动作信息表 - 存储动作信息的详细信息，包括动作、部门、状态等',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'action_info'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'动作', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'action';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'department';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'序号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'sequence_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'条码号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'barcode_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否控制数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'is_quantity_controlled';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否输入通过数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'is_passed_quantity_input_required';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否输入报废数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'is_scrapped_quantity_input_required';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否自带上工序数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'is_previous_process_quantity_carried_over';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'允许跳工序', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'allow_skip_process';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'不用输入数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'no_quantity_input_required';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'操作员代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'operator_code';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'操作人姓名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'operator_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'操作时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'operation_time';

-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志（0代表存在 1代表删除）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'action_info', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_action_info_action] ON [dbo].[action_info] ([action] ASC);
CREATE NONCLUSTERED INDEX [IX_action_info_department] ON [dbo].[action_info] ([department] ASC);
CREATE NONCLUSTERED INDEX [IX_action_info_status] ON [dbo].[action_info] ([status] ASC);
CREATE NONCLUSTERED INDEX [IX_action_info_operator_code] ON [dbo].[action_info] ([operator_code] ASC);
CREATE NONCLUSTERED INDEX [IX_action_info_operation_time] ON [dbo].[action_info] ([operation_time] ASC);
CREATE NONCLUSTERED INDEX [IX_action_info_del_flag] ON [dbo].[action_info] ([del_flag] ASC);
GO


PRINT '动作信息表 (action_info) 创建完成！'
PRINT '表包含 22 个字段，涵盖动作信息的详细信息和标准系统字段'
PRINT '已创建主键约束、索引和字段注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time, deleted'
GO

-- =============================================
-- 机台信息表 (machine_tool_info) - 越南ERP系统
-- 创建时间: 2025-01-09
-- 描述: 存储机台信息的详细信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[machine_tool_info]') AND type in (N'U'))
    DROP TABLE [dbo].[machine_tool_info]
GO

-- 创建机台信息表
CREATE TABLE [dbo].[machine_tool_info] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,                 -- 主键
    [machine_tool] NVARCHAR(50) NULL,                   -- 机床
    [resource] NVARCHAR(50) NULL,                       -- 资源
    [serial_no] INT NULL,                               -- 序号
    [is_scannable] NVARCHAR(1) NULL,                    -- 可扫描
    [grouping] NVARCHAR(50) NULL,                       -- 分组
    [category] NVARCHAR(50) NULL,                       -- 类别
    [usage_status] NVARCHAR(50) NULL,                   -- 使用状况
    [deactivation_date] SMALLDATETIME NULL,             -- 停用日期
    [remarks] NVARCHAR(300) NULL,                       -- 备注
    [operator] NVARCHAR(8) NULL,                        -- 操作人
    [operation_time] SMALLDATETIME NULL,                -- 操作时间

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                       -- 租户ID
    [create_dept] BIGINT NULL,                          -- 创建部门
    [create_by] BIGINT NULL,                            -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),           -- 创建时间
    [update_by] BIGINT NULL,                            -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),           -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                       -- 删除标志（0代表存在 1代表删除）

    CONSTRAINT [PK_machine_tool_info] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'机台信息表 - 存储机台信息的详细信息，包括机床、资源、使用状况等',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'machine_tool_info'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'机床', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'machine_tool';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'资源', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'resource';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'序号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'serial_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'可扫描', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'is_scannable';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'分组', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'grouping';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'类别', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'category';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'使用状况', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'usage_status';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'停用日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'deactivation_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'remarks';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'操作人', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'operator';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'操作时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'operation_time';

-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志（0代表存在 1代表删除）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'machine_tool_info', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_machine_tool] ON [dbo].[machine_tool_info] ([machine_tool] ASC);
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_resource] ON [dbo].[machine_tool_info] ([resource] ASC);
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_grouping] ON [dbo].[machine_tool_info] ([grouping] ASC);
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_category] ON [dbo].[machine_tool_info] ([category] ASC);
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_usage_status] ON [dbo].[machine_tool_info] ([usage_status] ASC);
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_operator] ON [dbo].[machine_tool_info] ([operator] ASC);
CREATE NONCLUSTERED INDEX [IX_machine_tool_info_del_flag] ON [dbo].[machine_tool_info] ([del_flag] ASC);
GO


PRINT '机台信息表 (machine_tool_info) 创建完成！'
PRINT '表包含 19 个字段，涵盖机台信息的详细信息和标准系统字段'
PRINT '已创建主键约束、索引和字段注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time, deleted'
GO

-- =============================================
-- 外发加工标签信息表 (outward_process_lab) - 越南ERP系统
-- 创建时间: 2025-01-27
-- 描述: 存储外发加工标签的详细信息
-- =============================================

-- 删除表（如果存在）
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[outward_process_lab]') AND type in (N'U'))
    DROP TABLE [dbo].[outward_process_lab]
GO

-- 创建外发加工标签信息表
CREATE TABLE [dbo].[outward_process_lab] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,                    -- 主键
    [prt_sw] NCHAR(1) NULL,                                -- PRT_SW
    [pr_no] NVARCHAR(20) NULL,                             -- PR号
    [pr_itm] NVARCHAR(10) NULL,                            -- PR行
    [pr_type] NVARCHAR(30) NULL,                           -- 申请类型
    [prd_no] NVARCHAR(20) NULL,                            -- 品号
    [prd_desc] NVARCHAR(250) NULL,                         -- 描述
    [qty] INT NULL,                                        -- 数量
    [ut] NVARCHAR(10) NULL,                                -- 单位
    [pr_date] DATETIME NULL,                               -- 请求日期
    [pmc_request_date] NVARCHAR(8) NULL,                   -- PMC要求日期
    [vendor_code] NVARCHAR(30) NULL,                       -- 厂商代号
    [vendor_snm] NVARCHAR(30) NULL,                        -- 简称
    [vendor_name] NVARCHAR(80) NULL,                       -- 厂商名
    [unit_price] FLOAT NULL,                               -- 单价
    [currency] NVARCHAR(10) NULL,                          -- 货币
    [is_provide_materials] NVARCHAR(10) NULL,              -- 是否供料
    [dwg_no] NVARCHAR(200) NULL,                           -- 图号
    [pj_no] NVARCHAR(30) NULL,                             -- PJ号
    [mo_no] NVARCHAR(30) NULL,                             -- MO号
    [purchase_num] NVARCHAR(30) NULL,                      -- 订单号
    [batch_no] NVARCHAR(30) NULL,                          -- 批号
    [sum_price] FLOAT NULL,                                -- 总价
    [sta] NVARCHAR(50) NULL,                               -- 状态
    [outsourcing_date] DATETIME NULL,                      -- 外发交期
    [cust_no] NVARCHAR(10) NULL,                           -- 客户代码
    [zc_no] NVARCHAR(10) NULL,                             -- 工序号
    [machining_center] NVARCHAR(20) NULL,                  -- 加工中心
    [zc_name] NVARCHAR(30) NULL,                           -- 工序名称
    [epf_snm] NVARCHAR(30) NULL,                           -- 电镀厂商
    [electroplate_content] NVARCHAR(300) NULL,             -- 电镀内容
    [specified_materials] NVARCHAR(10) NULL,               -- 指定材料
    [is_electroplate] NVARCHAR(10) NULL,                   -- 是否电镀
    [epf_code] NVARCHAR(10) NULL,                          -- 电镀厂商代号
    [epf_name] NVARCHAR(30) NULL,                          -- 电镀厂商名
    [usr] NVARCHAR(50) NULL,                               -- USR
    [sys_date] SMALLDATETIME NULL,                         -- SYS_DATE
    [host] NVARCHAR(50) NULL,                              -- host
    [rem] NVARCHAR(50) NULL,                               -- 备注

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                          -- 租户ID
    [create_dept] BIGINT NULL,                             -- 创建部门
    [create_by] BIGINT NULL,                               -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),              -- 创建时间
    [update_by] BIGINT NULL,                               -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),              -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                          -- 删除标志（0代表存在 1代表删除）

    CONSTRAINT [PK_outward_process_lab] PRIMARY KEY CLUSTERED ([id] ASC)
);
GO

-- 添加表注释
EXEC sys.sp_addextendedproperty
    @name = N'MS_Description',
    @value = N'外发加工标签信息表 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'outward_process_lab'
GO

-- 添加字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'主键', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PRT_SW', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'prt_sw';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PR号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'pr_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PR行', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'pr_itm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'申请类型', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'pr_type';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'品号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'prd_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'描述', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'prd_desc';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'数量', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'qty';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'单位', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'ut';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'请求日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'pr_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PMC要求日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'pmc_request_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'厂商代号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'vendor_code';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'简称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'vendor_snm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'厂商名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'vendor_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'单价', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'unit_price';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'货币', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'currency';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否供料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'is_provide_materials';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'图号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'dwg_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'PJ号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'pj_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'MO号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'mo_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'订单号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'purchase_num';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'批号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'batch_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'总价', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'sum_price';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'sta';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'外发交期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'outsourcing_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'客户代码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'cust_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工序号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'zc_no';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'加工中心', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'machining_center';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'工序名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'zc_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'电镀厂商', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'epf_snm';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'电镀内容', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'electroplate_content';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'指定材料', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'specified_materials';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'是否电镀', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'is_electroplate';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'电镀厂商代号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'epf_code';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'电镀厂商名', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'epf_name';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'USR', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'usr';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'SYS_DATE', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'sys_date';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'host', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'host';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'rem';

-- 标准系统字段注释
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'租户ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'tenant_id';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建部门', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'create_dept';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'create_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'create_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新者', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'update_by';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'update_time';
EXEC sys.sp_addextendedproperty @name = N'MS_Description', @value = N'删除标志（0代表存在 1代表删除）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'outward_process_lab', @level2type = N'COLUMN', @level2name = N'del_flag';
GO

-- 创建常用查询索引
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_pr_no] ON [dbo].[outward_process_lab] ([pr_no] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_prd_no] ON [dbo].[outward_process_lab] ([prd_no] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_mo_no] ON [dbo].[outward_process_lab] ([mo_no] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_vendor_code] ON [dbo].[outward_process_lab] ([vendor_code] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_cust_no] ON [dbo].[outward_process_lab] ([cust_no] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_sta] ON [dbo].[outward_process_lab] ([sta] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_pr_date] ON [dbo].[outward_process_lab] ([pr_date] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_outsourcing_date] ON [dbo].[outward_process_lab] ([outsourcing_date] ASC);
CREATE NONCLUSTERED INDEX [IX_outward_process_lab_del_flag] ON [dbo].[outward_process_lab] ([del_flag] ASC);
GO

PRINT '外发加工标签信息表 (outward_process_lab) 创建完成！'
PRINT '表包含 46 个字段，涵盖外发加工标签的详细信息和标准系统字段'
PRINT '已创建主键约束、索引和字段注释'
PRINT '已添加标准系统字段：tenant_id, create_dept, create_by, create_time, update_by, update_time, del_flag'
GO

-- 示例查询语句
/*
-- 查询最近24小时的同步统计
SELECT * FROM [dbo].[v_sync_statistics] WHERE [last_sync_time] >= DATEADD(DAY, -1, GETDATE());

-- 查询失败的同步记录
SELECT * FROM [dbo].[t_sync_log]
WHERE [sync_status] = 0 AND [sync_time] >= DATEADD(DAY, -1, GETDATE())
ORDER BY [sync_time] DESC;

-- 查询各模块的同步成功率
SELECT [module], [data_type], [success_rate] FROM [dbo].[v_sync_statistics] ORDER BY [success_rate] DESC;

-- 调用清理存储过程（清理30天前的日志）
EXEC [dbo].[sp_clean_expired_sync_logs] @days = 30;

-- 使用状态描述函数
SELECT [id], [module], [data_type], [sync_status], [dbo].[fn_get_sync_status_name]([sync_status]) as [status_name]
FROM [dbo].[t_sync_log]
WHERE [sync_time] >= DATEADD(DAY, -1, GETDATE());

-- 订单信息总表查询示例
-- 查询所有正常状态的订单
SELECT [id], [so_no], [mo_no], [customer_code], [project_no], [prd_no], [order_quantity], [order_date], [sta]
FROM [dbo].[Master]
WHERE [delete_flag] = 0
ORDER BY [order_date] DESC;

-- 查询指定客户的订单
SELECT [id], [so_no], [mo_no], [prd_no], [order_quantity], [promised_delivery_date]
FROM [dbo].[Master]
WHERE [customer_code] = 'C001' AND [delete_flag] = 0;

-- 查询急单
SELECT [id], [so_no], [mo_no], [customer_code], [urgent_flag], [promised_delivery_date]
FROM [dbo].[Master]
WHERE [urgent_flag] = '1' AND [delete_flag] = 0;

-- 排产信息表查询示例
-- 查询指定MO号的排产信息
SELECT [id], [aps_no], [mo_no], [zc_nm], [zgs], [b_dd], [e_dd], [dep]
FROM [dbo].[product_schedule_info]
WHERE [mo_no] = 'MO001' AND [del_flag] = 0
ORDER BY [zc_itm];

-- 查询指定时间范围内的排产计划
SELECT [id], [aps_no], [mo_no], [zc_nm], [b_dd], [e_dd], [jts]
FROM [dbo].[product_schedule_info]
WHERE [b_dd] >= '2025-01-01' AND [b_dd] <= '2025-01-31' AND [del_flag] = 0
ORDER BY [b_dd];

-- 查询指定机台的排产情况
SELECT [id], [aps_no], [mo_no], [zc_nm], [b_dd], [e_dd], [zgs]
FROM [dbo].[product_schedule_info]
WHERE [dep] = 'M001' AND [del_flag] = 0
ORDER BY [b_dd];

-- 问图记录表查询示例
-- 查询指定MO号的问图记录
SELECT [id], [mo_no], [drawing_no], [query_person], [query_time], [status]
FROM [dbo].[query_drawing_record]
WHERE [mo_no] = 'MO001' AND [del_flag] = 0
ORDER BY [query_time] DESC;

-- 查询待回复的问图记录
SELECT [id], [mo_no], [drawing_no], [query_person], [query_time], [deadline]
FROM [dbo].[query_drawing_record]
WHERE [engineering_reply_time] IS NULL AND [del_flag] = 0
ORDER BY [query_time];

-- 查询指定客户的问图记录
SELECT [id], [mo_no], [drawing_no], [query_person], [query_time], [engineering_status]
FROM [dbo].[query_drawing_record]
WHERE [customer_code] = 'C001' AND [del_flag] = 0
ORDER BY [query_time] DESC;

-- 扫描记录日志表查询示例
-- 查询指定MO号的扫描记录
SELECT [id], [mo_no], [process_name], [operator], [start_time], [end_time], [passed_quantity]
FROM [dbo].[scan_record_log]
WHERE [mo_no] = 'MO001' AND [del_flag] = 0
ORDER BY [start_time];

-- 查询指定操作员的扫描记录
SELECT [id], [mo_no], [process_name], [start_time], [end_time], [passed_quantity], [scrapped_quantity]
FROM [dbo].[scan_record_log]
WHERE [operator] = 'OP001' AND [del_flag] = 0
ORDER BY [start_time] DESC;

-- 查询指定时间范围内的扫描记录
SELECT [id], [mo_no], [process_name], [operator], [passed_quantity], [actual_time]
FROM [dbo].[scan_record_log]
WHERE [start_time] >= '2025-01-01' AND [start_time] <= '2025-01-31' AND [del_flag] = 0
ORDER BY [start_time];

-- 动作信息表查询示例
-- 查询指定部门的动作信息
SELECT [id], [action], [department], [status], [operator_code], [operation_time]
FROM [dbo].[action_info]
WHERE [department] = '生产部' AND [del_flag] = 0
ORDER BY [operation_time] DESC;

-- 查询需要控制数量的动作
SELECT [id], [action], [department], [is_quantity_controlled], [is_passed_quantity_input_required]
FROM [dbo].[action_info]
WHERE [is_quantity_controlled] = 1 AND [del_flag] = 0
ORDER BY [action];

-- 查询指定操作员的动作记录
SELECT [id], [action], [department], [operation_time], [status]
FROM [dbo].[action_info]
WHERE [operator_code] = 'OP001' AND [del_flag] = 0
ORDER BY [operation_time] DESC;

-- 机台信息表查询示例
-- 查询指定分组的机台信息
SELECT [id], [machine_tool], [resource], [grouping], [usage_status], [operator]
FROM [dbo].[machine_tool_info]
WHERE [grouping] = 'CNC组' AND [del_flag] = 0
ORDER BY [machine_tool];

-- 查询可扫描的机台
SELECT [id], [machine_tool], [resource], [category], [usage_status]
FROM [dbo].[machine_tool_info]
WHERE [is_scannable] = '1' AND [del_flag] = 0
ORDER BY [machine_tool];

-- 查询指定使用状况的机台
SELECT [id], [machine_tool], [resource], [grouping], [category], [operation_time]
FROM [dbo].[machine_tool_info]
WHERE [usage_status] = '正常' AND [del_flag] = 0
ORDER BY [operation_time] DESC;

-- 外发加工标签信息表查询示例
-- 查询指定PR号的外发加工标签
SELECT [id], [pr_no], [pr_itm], [prd_no], [prd_desc], [qty], [vendor_name], [sta]
FROM [dbo].[outward_process_lab]
WHERE [pr_no] = 'PR001' AND [del_flag] = 0
ORDER BY [pr_itm];

-- 查询指定厂商的外发加工标签
SELECT [id], [pr_no], [prd_no], [prd_desc], [qty], [unit_price], [sum_price], [outsourcing_date]
FROM [dbo].[outward_process_lab]
WHERE [vendor_code] = 'V001' AND [del_flag] = 0
ORDER BY [outsourcing_date];

-- 查询指定状态的外发加工标签
SELECT [id], [pr_no], [prd_no], [vendor_name], [qty], [pr_date], [outsourcing_date]
FROM [dbo].[outward_process_lab]
WHERE [sta] = '待处理' AND [del_flag] = 0
ORDER BY [pr_date];

-- 查询指定时间范围内的外发加工标签
SELECT [id], [pr_no], [prd_no], [vendor_name], [qty], [sum_price], [outsourcing_date]
FROM [dbo].[outward_process_lab]
WHERE [pr_date] >= '2025-01-01' AND [pr_date] <= '2025-01-31' AND [del_flag] = 0
ORDER BY [pr_date];

-- 查询需要电镀的外发加工标签
SELECT [id], [pr_no], [prd_no], [epf_name], [electroplate_content], [outsourcing_date]
FROM [dbo].[outward_process_lab]
WHERE [is_electroplate] = '是' AND [del_flag] = 0
ORDER BY [outsourcing_date];

-- 查询指定客户的外发加工标签
SELECT [id], [pr_no], [prd_no], [vendor_name], [qty], [unit_price], [sta]
FROM [dbo].[outward_process_lab]
WHERE [cust_no] = 'C001' AND [del_flag] = 0
ORDER BY [pr_date] DESC;
*/
