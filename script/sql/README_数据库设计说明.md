# 越南ERP系统数据库设计说明文档

## 📋 文档概述

本文档详细说明了越南ERP系统的数据库设计，包括表结构、字段定义、索引设计、命名规范等内容。该数据库设计基于SQL Server，采用标准化的表结构和字段命名规范。

## 🗂️ 数据库结构概览

### 表数量统计
- **总表数**: 8张表
- **业务表**: 6张表
- **系统表**: 2张表
- **总字段数**: 约200+个字段（Master表包含100+个字段）

### 表分类

| 分类 | 表名 | 中文名称 | 用途 |
|------|------|----------|------|
| 系统表 | `t_sync_log` | 同步日志表 | 记录数据同步操作日志 |
| 系统表 | `t_sync_config` | 同步配置表 | 存储同步配置参数 |
| 业务表 | `Master` | 订单信息总表 | 存储订单主数据 |
| 业务表 | `product_schedule_info` | 排产信息表 | 存储生产排产信息 |
| 业务表 | `query_drawing_record` | 问图记录表 | 存储图纸查询记录 |
| 业务表 | `scan_record_log` | 扫描记录日志表 | 存储生产扫描记录 |
| 业务表 | `action_info` | 动作信息表 | 存储动作配置信息 |
| 业务表 | `machine_tool_info` | 机台信息表 | 存储机台设备信息 |

## 🏗️ 表结构详细设计

### 1. 系统表设计

#### 1.1 同步日志表 (t_sync_log)

**用途**: 记录数据同步操作的详细日志信息

**字段结构**:
```sql
CREATE TABLE [dbo].[t_sync_log] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,           -- 主键
    [module] NVARCHAR(50) NOT NULL,               -- 模块名称
    [data_type] NVARCHAR(50) NOT NULL,            -- 数据类型
    [sync_type] NVARCHAR(20) NOT NULL,            -- 同步类型
    [sync_status] NVARCHAR(20) NOT NULL,          -- 同步状态
    [sync_time] DATETIME NOT NULL,                -- 同步时间
    [request_data] NVARCHAR(MAX) NULL,            -- 请求数据
    [response_data] NVARCHAR(MAX) NULL,           -- 响应数据
    [response_time] INT NULL,                     -- 响应时间（毫秒）
    [retry_count] INT DEFAULT 0,                  -- 重试次数
    [remark] NVARCHAR(500) NULL,                  -- 备注信息
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_t_sync_log] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_t_sync_log_module_type` - 模块和数据类型复合索引
- `IX_t_sync_log_sync_time` - 同步时间索引
- `IX_t_sync_log_sync_status` - 同步状态索引

#### 1.2 同步配置表 (t_sync_config)

**用途**: 存储数据同步的配置参数

**字段结构**:
```sql
CREATE TABLE [dbo].[t_sync_config] (
    [id] BIGINT IDENTITY(1,1) NOT NULL,           -- 主键
    [module] NVARCHAR(50) NOT NULL,               -- 模块名称
    [config_key] NVARCHAR(100) NOT NULL,          -- 配置键
    [config_value] NVARCHAR(500) NULL,            -- 配置值
    [description] NVARCHAR(200) NULL,             -- 配置说明
    [enabled] TINYINT DEFAULT 1,                  -- 是否启用: 1-启用 0-禁用
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_t_sync_config] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_t_sync_config_module_key` - 模块和配置键复合索引
- `IX_t_sync_config_enabled` - 启用状态索引

### 2. 业务表设计

#### 2.1 订单信息总表 (Master)

**用途**: 存储订单的主数据信息，包含完整的订单生命周期数据

**字段结构**:
```sql
CREATE TABLE [dbo].[Master] (
    -- 主键字段
    [id] DECIMAL(18,0) IDENTITY(1,1) NOT NULL,    -- 主键，自增

    -- 基础信息字段
    [sta] NVARCHAR(30) NULL,                      -- 状态
    [rem] NVARCHAR(100) NULL,                     -- 备注

    -- 订单日期信息
    [order_date] SMALLDATETIME NULL,              -- 接单日期
    [customer_req_delivery_date] SMALLDATETIME NULL, -- 客户要求交期
    [promised_delivery_date] SMALLDATETIME NULL,   -- 承诺交期
    [next_plan_date] SMALLDATETIME NULL,           -- 下计划日期
    [required_completion_date] SMALLDATETIME NULL, -- 要求完成日期
    [last_process_time] SMALLDATETIME NULL,        -- 末工序时间
    [warehouse_entry_date] SMALLDATETIME NULL,     -- 入仓日期
    [actual_delivery_date] SMALLDATETIME NULL,     -- 实际交期

    -- 客户和项目信息
    [customer_code] NVARCHAR(5) NULL,             -- 客户代码
    [project_no] NVARCHAR(20) NULL,               -- 项目号
    [project_manager] NVARCHAR(20) NULL,          -- 项目经理
    [customer_po_no] NVARCHAR(50) NULL,           -- 客户PO号
    [po_itm] NVARCHAR(10) NULL,                   -- PO_ITM

    -- 订单编号信息
    [so_no] NVARCHAR(20) NULL,                    -- SO号
    [so_itm] NVARCHAR(10) NULL,                   -- SO项次
    [mo_no] NVARCHAR(20) NULL,                    -- MO_NO (主键不重复)
    [parent_order_no] NVARCHAR(20) NULL,          -- 上层订单号

    -- 产品信息
    [part_classification] NVARCHAR(1) NULL,       -- 零件分类
    [prd_no] NVARCHAR(20) NULL,                   -- 品号
    [dwg_no] NVARCHAR(100) NULL,                  -- 图号
    [version] NVARCHAR(15) NULL,                  -- 版本
    [customer_product_no] NVARCHAR(50) NULL,      -- 客户产品料号
    [customer_product_name] NVARCHAR(50) NULL,    -- 客户产品名称
    [product_description] NVARCHAR(50) NULL,      -- 产品描述

    -- 数量信息
    [order_quantity] FLOAT NULL DEFAULT 0,        -- 订单数量
    [production_quantity] FLOAT NULL DEFAULT 0,   -- 生产数量
    [process_quantity] FLOAT NULL DEFAULT 0,      -- 工艺数量
    [scrap_quantity] FLOAT NULL DEFAULT 0,        -- 报废数量
    [in_warehouse_count] FLOAT NULL DEFAULT 0,    -- 已入仓数
    [outstanding_warehouse_count] FLOAT NULL DEFAULT 0, -- 欠入仓数
    [delivery_quantity] FLOAT NULL DEFAULT 0,     -- 交货数量
    [outstanding_shipment_quantity] FLOAT NULL DEFAULT 0, -- 欠出货数量
    [single_piece_net_weight] FLOAT NULL DEFAULT 0, -- 单件净重

    -- 单位和分类信息
    [unit] NVARCHAR(10) NULL,                     -- 单位
    [industry] NVARCHAR(10) NULL,                 -- 行业
    [order_category] NVARCHAR(20) NULL,           -- 订单类别
    [plan_type] NVARCHAR(10) NULL,                -- 计划类型

    -- 人员信息
    [order_placer] NVARCHAR(20) NULL,             -- 下单员
    [follow_up_staff] NVARCHAR(20) NULL,          -- 跟单员
    [bom_manager] NVARCHAR(20) NULL,              -- BOM负责人
    [process_compiler] NVARCHAR(50) NULL,         -- 工艺编制人

    -- 标识字段
    [merge_flag] NVARCHAR(1) NULL,                -- 合单标识
    [urgent_flag] NVARCHAR(1) NULL,               -- 急单标识
    [sample_flag] NVARCHAR(1) NULL,               -- 打样标识
    [has_standard_parts] NVARCHAR(1) NULL,        -- 是否有标准件
    [is_material_purchased] NVARCHAR(1) NULL,     -- 是否买料
    [is_electroplated] NVARCHAR(1) NULL,          -- 是否电镀

    -- 工艺和材料信息
    [action] NVARCHAR(50) NULL,                   -- 动作
    [current_process] NVARCHAR(30) NULL,          -- 当前工序
    [processing_material] NVARCHAR(600) NULL,     -- 加工材料
    [required_material] NVARCHAR(50) NULL,        -- 要求材料
    [quoted_material] NVARCHAR(50) NULL,          -- 报价材料
    [drawing_difficulty_level] NVARCHAR(10) NULL, -- 图纸难度等级
    [process_difficulty_level] NVARCHAR(10) NULL, -- 工艺难度等级
    [process_version] NVARCHAR(10) NULL,          -- 工艺版本

    -- 部门和成本信息
    [completion_department] NVARCHAR(50) NULL,    -- 完成部门 (供应商)
    [production_workshop] NVARCHAR(50) NULL,      -- 生产车间 (加工车间)
    [profit_center] NVARCHAR(20) NULL,            -- 利润中心
    [cost_center] NVARCHAR(15) NULL,              -- 成本中心

    -- 其他信息
    [charge_remarks] NVARCHAR(50) NULL,           -- 收费备注
    [close_order_reason] NVARCHAR(50) NULL,       -- 关单原因
    [sta1] INT NULL DEFAULT 0,                    -- 状态1

    -- 同步标记字段 (nchar(1))
    [so_document] NCHAR(1) NULL,                  -- SO单 (同步标记)
    [mo_document] NCHAR(1) NULL,                  -- MO号 (同步标记)
    [pmc_group] NCHAR(1) NULL,                    -- PMC分组 (同步标记)
    [process] NCHAR(1) NULL,                      -- 工艺 (同步标记)
    [production_scheduling] NCHAR(1) NULL,        -- 排产 (同步标记)
    [mrp] NCHAR(1) NULL,                          -- MRP (同步标记)
    [purchase_request] NCHAR(1) NULL,             -- 请购 (同步标记)
    [purchase_order] NCHAR(1) NULL,               -- 下采购单 (同步标记)
    [material_receipt] NCHAR(1) NULL,             -- 材料收货 (同步标记)
    [iqc_inspection] NCHAR(1) NULL,               -- IQC检测 (同步标记)
    [material_return] NCHAR(1) NULL,              -- 材料退货 (同步标记)
    [material_inbound] NCHAR(1) NULL,             -- 材料进仓 (同步标记)
    [subcontracting] NCHAR(1) NULL,               -- 托工 (同步标记)

    -- 同步天心单号字段
    [purchase_request_no] NVARCHAR(20) NULL,      -- 请购单号 (同步天心单号)
    [purchase_order_no] NVARCHAR(20) NULL,        -- 采购单号 (同步天心单号)
    [warehouse_entry_no] NVARCHAR(20) NULL,       -- 入库单号 (同步天心单号)
    [warehouse_exit_no] NVARCHAR(20) NULL,        -- 出库单号 (同步天心单号)
    [sales_order_no] NVARCHAR(20) NULL,           -- 销货单号 (同步天心单号)

    -- 删除标识
    [del_flag] FLOAT NULL DEFAULT 0,           -- 删除标识 (0-正常;1-删除)

    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    
    CONSTRAINT [PK_Master] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_Master_mo_no` - MO号索引
- `IX_Master_customer_code` - 客户代码索引
- `IX_Master_prd_no` - 品号索引
- `IX_Master_so_no` - SO号索引
- `IX_Master_order_date` - 接单日期索引
- `IX_Master_promised_delivery_date` - 承诺交期索引

#### 2.2 排产信息表 (product_schedule_info)

**用途**: 存储生产排产的详细信息

**字段结构**:
```sql
CREATE TABLE [dbo].[product_schedule_info] (
    [id] DECIMAL(18,0) IDENTITY(1,1) NOT NULL,    -- 主键
    [aps_no] NVARCHAR(50) NULL,                   -- 排产单号
    [itm] INT NULL,                               -- 排产项次
    [mo_no] NVARCHAR(50) NULL,                    -- MO号
    [zc_itm] INT NULL,                            -- 工序号
    [zc_nm] NVARCHAR(100) NULL,                   -- 工序名称
    [zc_no] NVARCHAR(50) NULL,                    -- 制程代号
    [zgs] DECIMAL(18,2) NULL,                     -- 总工时
    [dep] NVARCHAR(50) NULL,                      -- 机台
    [zgs_erp] DECIMAL(18,2) NULL,                 -- ERP总工时
    [waittime] DECIMAL(18,2) NULL,                -- 调试时间
    [kg_dd] DATETIME NULL,                        -- 开工时间
    [b_dd] DATETIME NULL,                         -- 排产开始时间
    [e_dd] DATETIME NULL,                         -- 排产结束时间
    [jts] INT NULL,                               -- 机台数
    [cus_no] NVARCHAR(50) NULL,                   -- 托工厂商
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_product_schedule_info] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_product_schedule_info_aps_no` - 排产单号索引
- `IX_product_schedule_info_mo_no` - MO号索引
- `IX_product_schedule_info_zc_no` - 制程代号索引
- `IX_product_schedule_info_b_dd` - 排产开始时间索引
- `IX_product_schedule_info_e_dd` - 排产结束时间索引

#### 2.3 问图记录表 (query_drawing_record)

**用途**: 存储图纸查询的完整记录信息

**字段结构**:
```sql
CREATE TABLE [dbo].[query_drawing_record] (
    [id] INT IDENTITY(1,1) NOT NULL,              -- 主键
    [status] NVARCHAR(50) NULL,                   -- 状态
    [deadline] SMALLDATETIME NULL,                -- 截止日期
    [mo_no] NVARCHAR(50) NULL,                    -- MO号
    [drawing_no] NVARCHAR(200) NULL,              -- 图号
    [query_process] NVARCHAR(50) NULL,            -- 问图工序
    [query_person] NVARCHAR(50) NULL,             -- 问图人
    [query_time] SMALLDATETIME NULL,              -- 问图时间
    [query_content] NVARCHAR(200) NULL,           -- 问图内容
    [engineering_reply_time] SMALLDATETIME NULL,  -- 工程回复时间
    [engineering_responsible] NVARCHAR(50) NULL,  -- 工程责任人
    [engineering_reply_content] NVARCHAR(200) NULL, -- 工程回复内容
    [engineering_status] NVARCHAR(50) NULL,       -- 工程状态
    [market_reply_time] SMALLDATETIME NULL,       -- 市场回复时间
    [market_responsible] NVARCHAR(50) NULL,       -- 市场责任人
    [market_reply_content] NVARCHAR(200) NULL,    -- 市场回复内容
    [query_type] NVARCHAR(10) NULL,               -- 问图类型
    [query_job_no] NVARCHAR(5) NULL,              -- 问图工号
    [process_compiler] NVARCHAR(50) NULL,         -- 工艺编制人
    [customer_code] NVARCHAR(5) NULL,             -- 客户代码
    [query_category] NVARCHAR(30) NULL,           -- 问图类别
    [feedback_type] NVARCHAR(30) NULL,            -- 反馈类型
    [order_placer] NVARCHAR(20) NULL,             -- 下单员
    [order_placer_name] NVARCHAR(20) NULL,        -- 下单员姓名
    [bom_responsible] NVARCHAR(20) NULL,          -- BOM责任人
    [project_manager] NVARCHAR(20) NULL,          -- 项目经理
    [extension_phone] NVARCHAR(20) NULL,          -- 分机电话
    [mobile_phone] NVARCHAR(20) NULL,             -- 手机号码
    [bom_no] NVARCHAR(20) NULL,                   -- BOM_NO
    [profit_center] NVARCHAR(20) NULL,            -- 利润中心
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_query_drawing_record] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_query_drawing_record_mo_no` - MO号索引
- `IX_query_drawing_record_drawing_no` - 图号索引
- `IX_query_drawing_record_query_time` - 问图时间索引
- `IX_query_drawing_record_status` - 状态索引
- `IX_query_drawing_record_customer_code` - 客户代码索引

#### 2.4 扫描记录日志表 (scan_record_log)

**用途**: 存储生产过程中的扫描记录

**字段结构**:
```sql
CREATE TABLE [dbo].[scan_record_log] (
    [id] DECIMAL(18,0) IDENTITY(1,1) NOT NULL,    -- 主键
    [mo_no] NVARCHAR(20) NULL,                    -- MO号
    [process_no] INT NULL,                        -- 工序号
    [process_name] NVARCHAR(50) NULL,             -- 工序名称
    [operator] NVARCHAR(50) NULL,                 -- 操作员
    [operator_name] NVARCHAR(50) NULL,            -- 姓名
    [machine_no] NVARCHAR(50) NULL,               -- 机台号
    [action] NVARCHAR(50) NULL,                   -- 动作
    [start_time] DATETIME NULL,                   -- 开始时间
    [end_time] DATETIME NULL,                     -- 结束时间
    [passed_quantity] INT NULL DEFAULT 0,         -- 通过数量
    [scrapped_quantity] INT NULL DEFAULT 0,       -- 报废数量
    [total_progress] FLOAT NULL DEFAULT 0,        -- 总进度
    [current_progress] FLOAT NULL DEFAULT 0,      -- 当前进度
    [process_man_hours] FLOAT NULL DEFAULT 0,     -- 工艺工时
    [actual_time] FLOAT NULL DEFAULT 0,           -- 实际时间
    [remark] NVARCHAR(200) NULL,                  -- 备注
    [customer_code] NVARCHAR(10) NULL,            -- 客户代码
    [supplier] NVARCHAR(50) NULL,                 -- 供应商
    [storage_location] NVARCHAR(50) NULL,         -- 储位
    [part_no] NVARCHAR(50) NULL,                  -- 品号
    [drawing_no] NVARCHAR(300) NULL,              -- 图号
    [version] NVARCHAR(20) NULL,                  -- 版本
    [material_description] NVARCHAR(300) NULL,    -- 物料描述
    [customer_product_no] NVARCHAR(50) NULL,      -- 客户产品料号
    [customer_product_name] NVARCHAR(100) NULL,   -- 客户产品名称
    [batch_no] NVARCHAR(50) NULL,                 -- 批号
    [computer_name] NVARCHAR(50) NULL,            -- 电脑名
    [industry] NVARCHAR(20) NULL,                 -- 行业
    [production_schedule_man_hours] FLOAT NULL DEFAULT 0, -- 排产工时
    [creation_time] DATETIME NULL,                -- 创建时间
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_scan_record_log] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_scan_record_log_mo_no` - MO号索引
- `IX_scan_record_log_process_no` - 工序号索引
- `IX_scan_record_log_operator` - 操作员索引
- `IX_scan_record_log_start_time` - 开始时间索引
- `IX_scan_record_log_end_time` - 结束时间索引
- `IX_scan_record_log_customer_code` - 客户代码索引

#### 2.5 动作信息表 (action_info)

**用途**: 存储动作配置的详细信息

**字段结构**:
```sql
CREATE TABLE [dbo].[action_info] (
    [id] INT IDENTITY(1,1) NOT NULL,              -- 主键
    [action] NVARCHAR(50) NULL,                   -- 动作
    [department] NVARCHAR(50) NULL,               -- 部门
    [status] NVARCHAR(1) NULL,                    -- 状态
    [sequence_no] INT NULL,                       -- 序号
    [barcode_no] NVARCHAR(10) NULL,               -- 条码号
    [is_quantity_controlled] BIT NULL,            -- 是否控制数量
    [is_passed_quantity_input_required] BIT NULL, -- 是否输入通过数量
    [is_scrapped_quantity_input_required] BIT NULL, -- 是否输入报废数量
    [is_previous_process_quantity_carried_over] BIT NULL, -- 是否自带上工序数量
    [allow_skip_process] BIT NULL,                -- 允许跳工序
    [no_quantity_input_required] BIT NULL,        -- 不用输入数量
    [operator_code] NVARCHAR(5) NULL,             -- 操作员代码
    [operator_name] NVARCHAR(8) NULL,             -- 操作人姓名
    [operation_time] SMALLDATETIME NULL,          -- 操作时间
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_action_info] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_action_info_action` - 动作索引
- `IX_action_info_department` - 部门索引
- `IX_action_info_status` - 状态索引
- `IX_action_info_operator_code` - 操作员代码索引
- `IX_action_info_operation_time` - 操作时间索引

#### 2.6 机台信息表 (machine_tool_info)

**用途**: 存储机台设备的详细信息

**字段结构**:
```sql
CREATE TABLE [dbo].[machine_tool_info] (
    [id] INT IDENTITY(1,1) NOT NULL,              -- 主键
    [machine_tool] NVARCHAR(50) NULL,             -- 机床
    [resource] NVARCHAR(50) NULL,                 -- 资源
    [serial_no] INT NULL,                         -- 序号
    [is_scannable] NVARCHAR(1) NULL,              -- 可扫描
    [grouping] NVARCHAR(50) NULL,                 -- 分组
    [category] NVARCHAR(50) NULL,                 -- 类别
    [usage_status] NVARCHAR(50) NULL,             -- 使用状况
    [deactivation_date] SMALLDATETIME NULL,       -- 停用日期
    [remarks] NVARCHAR(300) NULL,                 -- 备注
    [operator] NVARCHAR(8) NULL,                  -- 操作人
    [operation_time] SMALLDATETIME NULL,          -- 操作时间
    
    -- 标准系统字段
    [tenant_id] INT DEFAULT 1000,                 -- 租户ID
    [create_dept] BIGINT NULL,                    -- 创建部门
    [create_by] BIGINT NULL,                      -- 创建者
    [create_time] DATETIME DEFAULT GETDATE(),     -- 创建时间
    [update_by] BIGINT NULL,                      -- 更新者
    [update_time] DATETIME DEFAULT GETDATE(),     -- 更新时间
    [del_flag] TINYINT DEFAULT 0,                 -- 删除标志（0代表存在 1代表删除）
    
    CONSTRAINT [PK_machine_tool_info] PRIMARY KEY CLUSTERED ([id] ASC)
);
```

**索引设计**:
- `IX_machine_tool_info_machine_tool` - 机床索引
- `IX_machine_tool_info_resource` - 资源索引
- `IX_machine_tool_info_grouping` - 分组索引
- `IX_machine_tool_info_category` - 类别索引
- `IX_machine_tool_info_usage_status` - 使用状况索引
- `IX_machine_tool_info_operator` - 操作人索引

## 📏 设计规范

### 1. 命名规范

#### 1.1 表命名规范
- **系统表**: 使用 `t_` 前缀，如 `t_sync_log`、`t_sync_config`
- **业务表**: 使用有意义的英文名称，采用下划线分隔，如 `product_schedule_info`、`query_drawing_record`
- **表名**: 全部小写，使用下划线分隔单词

#### 1.2 字段命名规范
- **主键**: 统一使用 `id`
- **业务字段**: 使用有意义的英文名称，采用下划线分隔
- **系统字段**: 使用标准命名，如 `tenant_id`、`create_by`、`update_time`、`del_flag`
- **字段名**: 全部小写，使用下划线分隔单词

#### 1.3 索引命名规范
- **主键索引**: `PK_表名`
- **普通索引**: `IX_表名_字段名`
- **复合索引**: `IX_表名_字段1_字段2`

### 2. 数据类型规范

#### 2.1 主键类型
- **自增主键**: 使用 `BIGINT IDENTITY(1,1)` 或 `INT IDENTITY(1,1)`
- **业务主键**: 使用 `DECIMAL(18,0) IDENTITY(1,1)`

#### 2.2 字符串类型
- **短字符串**: 使用 `NVARCHAR(长度)`
- **长文本**: 使用 `NVARCHAR(MAX)`
- **固定长度**: 使用 `NCHAR(长度)`

#### 2.3 数值类型
- **整数**: 使用 `INT` 或 `BIGINT`
- **小数**: 使用 `DECIMAL(18,2)`
- **布尔值**: 使用 `BIT`

#### 2.4 日期时间类型
- **日期时间**: 使用 `DATETIME`
- **短日期时间**: 使用 `SMALLDATETIME`

### 3. 标准系统字段

所有业务表都包含以下标准系统字段：

| 字段名 | 数据类型 | 默认值 | 说明 |
|--------|----------|--------|------|
| `tenant_id` | `INT` | `1000` | 租户ID，支持多租户 |
| `create_dept` | `BIGINT` | `NULL` | 创建部门ID |
| `create_by` | `BIGINT` | `NULL` | 创建者ID |
| `create_time` | `DATETIME` | `GETDATE()` | 创建时间 |
| `update_by` | `BIGINT` | `NULL` | 更新者ID |
| `update_time` | `DATETIME` | `GETDATE()` | 更新时间 |
| `del_flag` | `TINYINT` | `0` | 删除标志（0代表存在 1代表删除） |

### 4. 索引设计原则

#### 4.1 主键索引
- 每个表都有聚集主键索引
- 主键使用自增ID

#### 4.2 业务索引
- 为常用查询字段创建索引
- 为外键字段创建索引
- 为时间字段创建索引

#### 4.3 复合索引
- 为多字段查询创建复合索引
- 考虑查询频率和选择性

## 🔧 使用说明

### 1. 数据库创建

执行SQL文件创建数据库结构：
```sql
-- 执行完整的SQL文件
-- 文件路径: script/sql/tianxin_sync_tables_sqlserver.sql
```

### 2. 常用查询示例

#### 2.1 查询未删除的记录
```sql
-- 查询所有未删除的订单
SELECT * FROM [dbo].[Master] WHERE [del_flag] = 0;

-- 查询指定客户的订单
SELECT * FROM [dbo].[Master] 
WHERE [customer_code] = 'CUST001' AND [del_flag] = 0;

-- 查询指定MO号的订单
SELECT [mo_no], [prd_no], [order_quantity], [promised_delivery_date]
FROM [dbo].[Master] 
WHERE [mo_no] = 'MO202501001' AND [del_flag] = 0;
```

#### 2.2 查询排产信息
```sql
-- 查询指定MO号的排产信息
SELECT * FROM [dbo].[product_schedule_info] 
WHERE [mo_no] = 'MO202501001' AND [del_flag] = 0;

-- 查询指定时间范围内的排产
SELECT * FROM [dbo].[product_schedule_info] 
WHERE [b_dd] >= '2025-01-01' AND [e_dd] <= '2025-01-31' 
AND [del_flag] = 0;
```

#### 2.3 查询扫描记录
```sql
-- 查询指定操作员的扫描记录
SELECT * FROM [dbo].[scan_record_log] 
WHERE [operator] = 'OP001' AND [del_flag] = 0
ORDER BY [start_time] DESC;

-- 查询指定时间范围内的扫描记录
SELECT * FROM [dbo].[scan_record_log] 
WHERE [start_time] >= '2025-01-01' AND [start_time] <= '2025-01-31' 
AND [del_flag] = 0;
```

#### 2.4 软删除操作
```sql
-- 软删除记录
UPDATE [dbo].[Master] 
SET [del_flag] = 1, [update_by] = 1001, [update_time] = GETDATE()
WHERE [id] = 123;

-- 恢复删除的记录
UPDATE [dbo].[Master] 
SET [del_flag] = 0, [update_by] = 1001, [update_time] = GETDATE()
WHERE [id] = 123;

-- 软删除排产信息
UPDATE [dbo].[product_schedule_info] 
SET [del_flag] = 1, [update_by] = 1001, [update_time] = GETDATE()
WHERE [id] = 456;
```

### 3. 数据同步

#### 3.1 同步配置
```sql
-- 查询同步配置
SELECT * FROM [dbo].[t_sync_config] 
WHERE [module] = 'order' AND [enabled] = 1;

-- 更新同步配置
UPDATE [dbo].[t_sync_config] 
SET [config_value] = 'new_value', [update_time] = GETDATE()
WHERE [module] = 'order' AND [config_key] = 'sync_interval';
```

#### 3.2 同步日志
```sql
-- 查询同步日志
SELECT * FROM [dbo].[t_sync_log] 
WHERE [module] = 'order' AND [sync_status] = 'SUCCESS'
ORDER BY [sync_time] DESC;

-- 查询失败的同步记录
SELECT * FROM [dbo].[t_sync_log] 
WHERE [sync_status] = 'FAILED' 
AND [sync_time] >= DATEADD(DAY, -1, GETDATE());
```

## 🚀 性能优化建议

### 1. 索引优化
- 定期分析查询执行计划
- 根据实际查询模式调整索引
- 避免创建过多不必要的索引

### 2. 查询优化
- 使用适当的WHERE条件
- 避免SELECT *，只查询需要的字段
- 使用分页查询处理大量数据

### 3. 数据维护
- 定期清理历史数据
- 监控表空间使用情况
- 定期更新统计信息

## 📝 注意事项

### 1. 数据完整性
- 所有表都使用软删除机制
- 重要字段建议添加NOT NULL约束
- 外键关系需要在应用层维护

### 2. 多租户支持
- 所有查询都需要包含tenant_id条件
- 数据隔离通过tenant_id实现
- 默认tenant_id为1000

### 3. 时间字段
- 创建时间和更新时间使用DATETIME类型
- 默认值为GETDATE()
- 业务时间字段根据实际需要选择类型

### 4. 字符编码
- 所有字符串字段使用NVARCHAR类型
- 支持Unicode字符
- 长度根据实际业务需求设置

## 📞 技术支持

如有问题，请联系开发团队：
- 数据库设计相关问题
- 性能优化建议
- 业务逻辑实现指导

---

**文档版本**: v1.0  
**创建时间**: 2025-01-09  
**最后更新**: 2025-01-09  
**维护人员**: 开发团队
