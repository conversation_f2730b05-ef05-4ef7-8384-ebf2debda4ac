spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          url: ${datasource.world-tianxin.url}
          username: ${datasource.world-tianxin.username}
          password: ${datasource.world-tianxin.password}

tianxin:
  api:
    # 基础配置
    base-url: http://api.amtxts.com
    app-id: your_app_id
    app-secret: your_app_secret
    timeout: 30000

    # 重试配置
    retry-times: 3
    retry-interval: 1000

    # 熔断器配置
    circuit-breaker-enabled: true
    circuit-breaker-failure-threshold: 5
    circuit-breaker-timeout: 60000
    circuit-breaker-half-open-max-calls: 3

    # 连接池配置
    max-connections: 100
    max-connections-per-route: 20
    connection-timeout: 5000
    read-timeout: 30000
# SAP配置
sap:
  ashost: **************
  systemNumber: "00"
  client: "320"
  user: IA01
  passwd: world098123
  language: ZH
  poolSize: 10
  peakLimit: 20
  abap-as-pooled: ABAP_AS_WITH_POOL
