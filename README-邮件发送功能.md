# 外发加工标签邮件发送功能

## 功能概述

本功能为外发加工标签管理系统提供简单的邮件发送能力，支持发送包含外发加工标签详细信息的邮件通知。

## 功能特性

- ✅ 支持发送文本和HTML格式邮件
- ✅ 支持多收件人（逗号分隔）
- ✅ 自动包含外发加工标签详细信息
- ✅ 支持自定义邮件内容
- ✅ 完整的权限控制和日志记录
- ✅ 异常处理和错误提示

## API接口说明

### 发送邮件接口

**接口地址：** `POST /processLab/sendMail`

**权限要求：** `tianxin:processLab:mail`

**请求参数：**

```json
{
  "to": "<EMAIL>,<EMAIL>",
  "subject": "外发加工标签通知",
  "content": "这是邮件的基础内容",
  "isHtml": true,
  "processLabIds": [1, 2, 3]
}
```

**参数说明：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| to | String | 是 | 收件人邮箱地址，多个用逗号分隔 |
| subject | String | 是 | 邮件主题 |
| content | String | 是 | 邮件内容 |
| isHtml | Boolean | 否 | 是否为HTML格式，默认false |
| processLabIds | List<Long> | 否 | 外发加工标签ID列表，会自动包含相关信息 |

**响应示例：**

```json
{
  "code": 200,
  "msg": "邮件发送成功",
  "data": null
}
```

## 使用示例

### 1. 发送简单文本邮件

```javascript
// 前端调用示例
const sendSimpleEmail = async () => {
  const data = {
    to: "<EMAIL>",
    subject: "外发加工通知",
    content: "请查收外发加工相关信息。",
    isHtml: false
  };
  
  const response = await request.post('/processLab/sendMail', data);
  console.log(response);
};
```

### 2. 发送包含外发加工标签信息的HTML邮件

```javascript
// 前端调用示例
const sendProcessLabEmail = async () => {
  const data = {
    to: "<EMAIL>,<EMAIL>",
    subject: "外发加工标签详细信息",
    content: "<h2>外发加工通知</h2><p>以下是相关的外发加工标签信息：</p>",
    isHtml: true,
    processLabIds: [1, 2, 3]
  };

  const response = await request.post('/processLab/sendMail', data);
  console.log(response);
};
```

### 3. Vue组件中的使用示例

```vue
<template>
  <div>
    <el-form :model="mailForm" ref="mailFormRef" :rules="mailRules">
      <el-form-item label="收件人" prop="to">
        <el-input v-model="mailForm.to" placeholder="多个邮箱用逗号分隔" />
      </el-form-item>
      
      <el-form-item label="抄送人" prop="cc">
        <el-input v-model="mailForm.cc" placeholder="多个邮箱用逗号分隔" />
      </el-form-item>
      
      <el-form-item label="邮件主题" prop="subject">
        <el-input v-model="mailForm.subject" />
      </el-form-item>
      
      <el-form-item label="邮件内容" prop="content">
        <el-input type="textarea" v-model="mailForm.content" :rows="5" />
      </el-form-item>
      
      <el-form-item label="HTML格式">
        <el-switch v-model="mailForm.isHtml" />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="sendMail" :loading="sending">
          发送邮件
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { sendProcessLabMail } from '@/api/tianxin/processLab';

const mailFormRef = ref();
const sending = ref(false);

const mailForm = reactive({
  to: '',
  subject: '',
  content: '',
  isHtml: false,
  processLabIds: []
});

const mailRules = {
  to: [{ required: true, message: '收件人不能为空', trigger: 'blur' }],
  subject: [{ required: true, message: '邮件主题不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '邮件内容不能为空', trigger: 'blur' }]
};

const sendMail = async () => {
  try {
    await mailFormRef.value.validate();
    sending.value = true;
    
    await sendProcessLabMail(mailForm);
    ElMessage.success('邮件发送成功');
    
    // 重置表单
    mailFormRef.value.resetFields();
  } catch (error) {
    ElMessage.error('邮件发送失败：' + error.message);
  } finally {
    sending.value = false;
  }
};
</script>
```

## 邮件内容格式

### 文本格式示例

```
这是邮件的基础内容

=== 外发加工标签信息 ===

--- 外发加工标签 - ID: 1 ---
PR号: PR202509001
品号: ABC123
描述: 精密零件加工
数量: 100 PCS
厂商: 精密加工厂 (V001)
状态: 进行中
外发交期: 2025-09-20
--------------------------------
```

### HTML格式示例

```html
<p>这是邮件的基础内容</p>

=== 外发加工标签信息 ===

<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>
<h4>外发加工标签 - ID: 1</h4>
<p><strong>PR号:</strong> PR202509001</p>
<p><strong>品号:</strong> ABC123</p>
<p><strong>描述:</strong> 精密零件加工</p>
<p><strong>数量:</strong> 100 PCS</p>
<p><strong>厂商:</strong> 精密加工厂 (V001)</p>
<p><strong>状态:</strong> 进行中</p>
<p><strong>外发交期:</strong> 2025-09-20</p>
</div>
```

## 配置说明

邮件发送功能依赖于系统的邮件配置，请确保在配置文件中正确设置了邮件服务器信息：

```yaml
mail:
  enabled: true
  host: smtp.example.com
  port: 587
  auth: true
  from: <EMAIL>
  user: <EMAIL>
  pass: your-password
  starttlsEnable: true
  sslEnable: false
```

## 权限配置

需要为用户分配 `tianxin:processLab:mail` 权限才能使用邮件发送功能。

## 注意事项

1. 邮件地址格式必须正确，系统会进行基本的格式验证
2. 多个邮件地址使用逗号或分号分隔
3. HTML格式邮件支持基本的HTML标签
4. 外发加工标签信息会自动追加到邮件内容末尾
5. 发送失败时会返回具体的错误信息
6. 所有邮件发送操作都会记录到系统日志中

## 扩展功能

未来可以考虑添加以下功能：

- [ ] 邮件模板支持
- [ ] 附件上传和发送
- [ ] 邮件发送历史记录
- [ ] 定时邮件发送
- [ ] 邮件发送状态跟踪
